<Window x:Class="Osama.UI.Views.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام أسامة - النافذة الرئيسية" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid>
                <TextBlock Text="نظام أسامة - إدارة شركات الإنترنت" 
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Foreground="White"
                           FontSize="18"
                           FontWeight="Bold"/>
                
                <Button Content="تسجيل الخروج"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Margin="20,0"
                        Padding="15,5"
                        Background="#F44336"
                        Foreground="White"
                        BorderThickness="0"
                        Click="LogoutButton_Click"/>
            </Grid>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <TextBlock Grid.Row="0" 
                       Text="مرحباً بك في نظام أسامة لإدارة شركات الإنترنت"
                       FontSize="24"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Margin="0,20"/>
            
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- بطاقات الوظائف -->
                <Border Grid.Row="0" Grid.Column="0" 
                        Background="#4CAF50" 
                        CornerRadius="10" 
                        Margin="10"
                        Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="المبيعات" 
                                   Foreground="White" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة المبيعات والاشتراكات" 
                                   Foreground="White" 
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
                
                <Border Grid.Row="0" Grid.Column="1" 
                        Background="#FF9800" 
                        CornerRadius="10" 
                        Margin="10"
                        Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="المخزون" 
                                   Foreground="White" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة المخزون والمنتجات" 
                                   Foreground="White" 
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
                
                <Border Grid.Row="0" Grid.Column="2" 
                        Background="#9C27B0" 
                        CornerRadius="10" 
                        Margin="10"
                        Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="العملاء" 
                                   Foreground="White" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة العملاء والموزعين" 
                                   Foreground="White" 
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
                
                <Border Grid.Row="1" Grid.Column="0" 
                        Background="#F44336" 
                        CornerRadius="10" 
                        Margin="10"
                        Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="الخزينة" 
                                   Foreground="White" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة الخزينة والمحاسبة" 
                                   Foreground="White" 
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
                
                <Border Grid.Row="1" Grid.Column="1" 
                        Background="#607D8B" 
                        CornerRadius="10" 
                        Margin="10"
                        Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="التقارير" 
                                   Foreground="White" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="التقارير والإحصائيات" 
                                   Foreground="White" 
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
                
                <Border Grid.Row="1" Grid.Column="2" 
                        Background="#795548" 
                        CornerRadius="10" 
                        Margin="10"
                        Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="الإعدادات" 
                                   Foreground="White" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="إعدادات النظام" 
                                   Foreground="White" 
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</Window>
