using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Osama.Data;
using Osama.Data.Repositories;
using Osama.Services;
using Osama.ViewModels;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace OsamaSimple
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // إنشاء Host للتطبيق
                _host = Host.CreateDefaultBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        // تكوين قاعدة البيانات
                        services.AddDbContext<OsamaDbContext>(options =>
                            options.UseSqlite("Data Source=OsamaDB.db"));

                        // تسجيل المستودعات
                        services.AddScoped<IUnitOfWork, UnitOfWork>();

                        // تسجيل الخدمات
                        services.AddScoped<AuthenticationService>();
                        services.AddScoped<SalesService>();
                        services.AddScoped<InventoryService>();
                        services.AddScoped<FinancialService>();

                        // تسجيل ViewModels
                        services.AddTransient<LoginViewModel>();
                        services.AddTransient<DashboardViewModel>();
                    })
                    .Build();

                // بدء Host
                await _host.StartAsync();

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                await EnsureDatabaseCreatedAsync();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            base.OnExit(e);
        }

        /// <summary>
        /// الحصول على خدمة من DI Container
        /// </summary>
        public static T GetService<T>() where T : class
        {
            return ((App)Current)._host?.Services.GetRequiredService<T>()
                ?? throw new InvalidOperationException($"Service {typeof(T).Name} not found");
        }

        /// <summary>
        /// التأكد من إنشاء قاعدة البيانات
        /// </summary>
        private async Task EnsureDatabaseCreatedAsync()
        {
            try
            {
                using var scope = _host?.Services.CreateScope();
                var context = scope?.ServiceProvider.GetRequiredService<OsamaDbContext>();

                if (context != null)
                {
                    await context.Database.EnsureCreatedAsync();

                    // إنشاء البيانات الأساسية إذا كانت قاعدة البيانات فارغة
                    if (!context.Users.Any())
                    {
                        await DataSeeder.SeedAsync(context);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
