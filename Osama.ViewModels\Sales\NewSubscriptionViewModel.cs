using Microsoft.Toolkit.Mvvm.Input;
using Osama.Data.Repositories;
using Osama.Models;
using Osama.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace Osama.ViewModels.Sales
{
    /// <summary>
    /// نموذج عرض اشتراك جديد
    /// </summary>
    public class NewSubscriptionViewModel : BaseViewModel
    {
        private readonly SalesService _salesService;
        private readonly IUnitOfWork _unitOfWork;
        
        // الحقول
        private string _customerName = string.Empty;
        private string _phoneNumber = string.Empty;
        private string _address = string.Empty;
        private decimal _subscriptionFee = 50000; // القيمة الافتراضية
        private Product? _selectedRouter;
        private string _notes = string.Empty;
        private bool _hasSuccess;
        private string _successMessage = string.Empty;
        private Sale? _lastSale;

        // المجموعات
        private ObservableCollection<Product> _routerProducts = new();

        public NewSubscriptionViewModel(SalesService salesService, IUnitOfWork unitOfWork)
        {
            _salesService = salesService;
            _unitOfWork = unitOfWork;

            // إنشاء الأوامر
            SaveCommand = new AsyncRelayCommand(SaveAsync, () => CanSave);
            PrintCommand = new AsyncRelayCommand(PrintAsync, () => CanPrint);
            ClearCommand = new RelayCommand(Clear);

            // تحميل البيانات
            _ = LoadDataAsync();
        }

        #region الخصائص

        /// <summary>
        /// اسم المشترك
        /// </summary>
        [Required(ErrorMessage = "اسم المشترك مطلوب")]
        public string CustomerName
        {
            get => _customerName;
            set
            {
                SetProperty(ref _customerName, value);
                SaveCommand.NotifyCanExecuteChanged();
                OnPropertyChanged(nameof(TotalAmount));
            }
        }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string PhoneNumber
        {
            get => _phoneNumber;
            set => SetProperty(ref _phoneNumber, value);
        }

        /// <summary>
        /// العنوان
        /// </summary>
        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        /// <summary>
        /// رسم الاشتراك
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "رسم الاشتراك يجب أن يكون أكبر من الصفر")]
        public decimal SubscriptionFee
        {
            get => _subscriptionFee;
            set
            {
                SetProperty(ref _subscriptionFee, value);
                OnPropertyChanged(nameof(TotalAmount));
            }
        }

        /// <summary>
        /// الراوتر المحدد
        /// </summary>
        public Product? SelectedRouter
        {
            get => _selectedRouter;
            set
            {
                SetProperty(ref _selectedRouter, value);
                OnPropertyChanged(nameof(TotalAmount));
            }
        }

        /// <summary>
        /// الملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// هل يوجد رسالة نجاح
        /// </summary>
        public bool HasSuccess
        {
            get => _hasSuccess;
            set => SetProperty(ref _hasSuccess, value);
        }

        /// <summary>
        /// رسالة النجاح
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// المبلغ الإجمالي
        /// </summary>
        public decimal TotalAmount
        {
            get
            {
                decimal routerPrice = SelectedRouter?.SalePrice ?? 0;
                return SubscriptionFee + routerPrice;
            }
        }

        /// <summary>
        /// منتجات الراوتر
        /// </summary>
        public ObservableCollection<Product> RouterProducts
        {
            get => _routerProducts;
            set => SetProperty(ref _routerProducts, value);
        }

        #endregion

        #region الأوامر

        /// <summary>
        /// أمر الحفظ
        /// </summary>
        public IAsyncRelayCommand SaveCommand { get; }

        /// <summary>
        /// أمر الطباعة
        /// </summary>
        public IAsyncRelayCommand PrintCommand { get; }

        /// <summary>
        /// أمر المسح
        /// </summary>
        public ICommand ClearCommand { get; }

        /// <summary>
        /// هل يمكن الحفظ
        /// </summary>
        private bool CanSave => !string.IsNullOrWhiteSpace(CustomerName) && SubscriptionFee > 0 && !IsBusy;

        /// <summary>
        /// هل يمكن الطباعة
        /// </summary>
        public bool CanPrint => _lastSale != null && !IsBusy;

        #endregion

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // تحميل منتجات الراوتر
                var routers = await _unitOfWork.Products.FindAsync(p => p.Category == ProductCategory.راوتر && p.IsActive);
                RouterProducts.Clear();
                foreach (var router in routers)
                {
                    RouterProducts.Add(router);
                }

            }, "جاري تحميل البيانات...");
        }

        /// <summary>
        /// حفظ الاشتراك
        /// </summary>
        private async Task SaveAsync()
        {
            if (!ValidateData())
                return;

            var result = await ExecuteAsync(async () =>
            {
                var sale = await _salesService.CreateNewSubscriptionAsync(
                    CustomerName,
                    SubscriptionFee,
                    SelectedRouter?.ProductId,
                    PhoneNumber,
                    Address,
                    Notes
                );

                if (sale == null)
                {
                    SetError("فشل في حفظ الاشتراك");
                    return;
                }

                _lastSale = sale;
                SetSuccess("تم حفظ الاشتراك بنجاح");
                OnPropertyChanged(nameof(CanPrint));

            }, "جاري حفظ الاشتراك...");
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private async Task PrintAsync()
        {
            // إذا لم يتم الحفظ، احفظ أولاً
            if (_lastSale == null)
            {
                await SaveAsync();
                if (_lastSale == null) return;
            }

            await ExecuteAsync(async () =>
            {
                // هنا يتم تنفيذ عملية الطباعة
                // يمكن استخدام مكتبة طباعة أو تصدير PDF
                await Task.Delay(1000); // محاكاة عملية الطباعة

                SetSuccess("تم طباعة الفاتورة بنجاح");

            }, "جاري طباعة الفاتورة...");
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void Clear()
        {
            CustomerName = string.Empty;
            PhoneNumber = string.Empty;
            Address = string.Empty;
            SubscriptionFee = 50000;
            SelectedRouter = null;
            Notes = string.Empty;
            _lastSale = null;
            
            ClearError();
            ClearSuccess();
            
            OnPropertyChanged(nameof(CanPrint));
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public override bool ValidateData()
        {
            ClearError();
            ClearSuccess();

            if (string.IsNullOrWhiteSpace(CustomerName))
            {
                SetError("اسم المشترك مطلوب");
                return false;
            }

            if (SubscriptionFee <= 0)
            {
                SetError("رسم الاشتراك يجب أن يكون أكبر من الصفر");
                return false;
            }

            return true;
        }

        /// <summary>
        /// تعيين رسالة نجاح
        /// </summary>
        private void SetSuccess(string message)
        {
            SuccessMessage = message;
            HasSuccess = true;
        }

        /// <summary>
        /// مسح رسالة النجاح
        /// </summary>
        private void ClearSuccess()
        {
            SuccessMessage = string.Empty;
            HasSuccess = false;
        }
    }
}