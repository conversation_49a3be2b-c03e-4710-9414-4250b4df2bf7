using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج معاملات المخزون
    /// </summary>
    public class InventoryTransaction
    {
        [Key]
        public int TransactionId { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public InventoryTransactionType Type { get; set; }

        public int Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? ReferenceId { get; set; } // مرجع للعملية (مبيعة، مشتريات، إلخ)

        // العلاقات
        public virtual Product Product { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// مخزون العمال
    /// </summary>
    public class WorkerInventory
    {
        [Key]
        public int WorkerInventoryId { get; set; }

        [Required]
        public int UserId { get; set; } // العامل

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual ICollection<WorkerInventoryItem> Items { get; set; } = new List<WorkerInventoryItem>();
    }

    /// <summary>
    /// عناصر مخزون العامل
    /// </summary>
    public class WorkerInventoryItem
    {
        [Key]
        public int ItemId { get; set; }

        [Required]
        public int WorkerInventoryId { get; set; }

        [Required]
        public int ProductId { get; set; }

        public int Quantity { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // العلاقات
        public virtual WorkerInventory WorkerInventory { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    /// <summary>
    /// أوامر الصيانة
    /// </summary>
    public class MaintenanceOrder
    {
        [Key]
        public int OrderId { get; set; }

        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime OrderDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual ICollection<MaintenanceOrderItem> Items { get; set; } = new List<MaintenanceOrderItem>();
    }

    /// <summary>
    /// عناصر أمر الصيانة
    /// </summary>
    public class MaintenanceOrderItem
    {
        [Key]
        public int ItemId { get; set; }

        [Required]
        public int OrderId { get; set; }

        [Required]
        public int ProductId { get; set; }

        public int Quantity { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual MaintenanceOrder Order { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    /// <summary>
    /// أنواع معاملات المخزون
    /// </summary>
    public enum InventoryTransactionType
    {
        إضافة = 1,          // إضافة للمخزون (مشتريات)
        سحب = 2,            // سحب من المخزون (مبيعات)
        تسليم_عامل = 3,     // تسليم لعامل
        إرجاع_عامل = 4,     // إرجاع من عامل
        صيانة = 5,          // صيانة
        تلف = 6,            // تلف
        جرد = 7             // جرد
    }
}