<Page x:Class="Osama.UI.Pages.Sales.PackageRenewalPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="تجديد باقة"
      FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="تجديد باقة العميل"
                   Style="{StaticResource HeaderStyle}"
                   Margin="0,0,0,24"/>

        <!-- المحتوى -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- نموذج التجديد -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                <StackPanel>
                    <!-- رسائل الحالة -->
                    <StackPanel>
                        <!-- رسالة الخطأ -->
                        <Border Background="#FFEBEE"
                                BorderBrush="{StaticResource ErrorBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="12"
                                Margin="0,0,0,8"
                                Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                         Foreground="{StaticResource ErrorBrush}"
                                                         Width="20" Height="20"
                                                         Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding ErrorMessage}"
                                           Style="{StaticResource ErrorTextStyle}"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- رسالة النجاح -->
                        <Border Background="#E8F5E8"
                                BorderBrush="{StaticResource SuccessBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="12"
                                Margin="0,0,0,16"
                                Visibility="{Binding HasSuccess, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CheckCircle"
                                                         Foreground="{StaticResource SuccessBrush}"
                                                         Width="20" Height="20"
                                                         Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding SuccessMessage}"
                                           Style="{StaticResource SuccessTextStyle}"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- حقول الإدخال -->
                    <StackPanel>
                        <!-- اسم المشترك -->
                        <ComboBox Style="{StaticResource InputComboBoxStyle}"
                                  materialDesign:HintAssist.Hint="اسم المشترك *"
                                  ItemsSource="{Binding Customers}"
                                  SelectedItem="{Binding SelectedCustomer}"
                                  DisplayMemberPath="FullName"
                                  IsEditable="True"
                                  Margin="0,0,0,16"/>

                        <!-- معلومات الباقة الحالية -->
                        <Border Background="#FFF3E0"
                                BorderBrush="{StaticResource WarningBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="12"
                                Margin="0,0,0,16"
                                Visibility="{Binding HasCurrentPackage, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="معلومات الباقة الحالية"
                                           Style="{StaticResource SubHeaderStyle}"
                                           Margin="0,0,0,8"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="الباقة الحالية:" FontSize="12" Margin="0,2"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentPackageName}" FontSize="12" Margin="8,2,0,2"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الانتهاء:" FontSize="12" Margin="0,2"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding PackageEndDate, StringFormat=yyyy/MM/dd}" FontSize="12" Margin="8,2,0,2"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="الحالة:" FontSize="12" Margin="0,2"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PackageStatus}" FontSize="12" Margin="8,2,0,2">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsPackageExpired}" Value="True">
                                                        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsPackageExpired}" Value="False">
                                                        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- نوع الباقة الجديدة -->
                        <ComboBox Style="{StaticResource InputComboBoxStyle}"
                                  materialDesign:HintAssist.Hint="نوع الباقة الجديدة *"
                                  ItemsSource="{Binding Packages}"
                                  SelectedItem="{Binding SelectedPackage}"
                                  DisplayMemberPath="Name"
                                  Margin="0,0,0,16"/>

                        <!-- سعر الباقة -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="سعر الباقة (ل.س)"
                                 Text="{Binding PackagePrice, UpdateSourceTrigger=PropertyChanged}"
                                 IsReadOnly="True"
                                 Margin="0,0,0,16"/>

                        <!-- مدة الباقة -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="مدة الباقة (يوم)"
                                 Text="{Binding PackageDuration, UpdateSourceTrigger=PropertyChanged}"
                                 IsReadOnly="True"
                                 Margin="0,0,0,16"/>

                        <!-- تواريخ التجديد -->
                        <Border Background="#E3F2FD"
                                BorderBrush="{StaticResource PrimaryBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="12"
                                Margin="0,0,0,16"
                                Visibility="{Binding HasSelectedPackage, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="تاريخ البداية"
                                               Style="{StaticResource LabelStyle}"
                                               HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding RenewalStartDate, StringFormat=yyyy/MM/dd}"
                                               Style="{StaticResource BodyTextStyle}"
                                               HorizontalAlignment="Center"
                                               FontFamily="{StaticResource ArabicFontBold}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="تاريخ الانتهاء"
                                               Style="{StaticResource LabelStyle}"
                                               HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding RenewalEndDate, StringFormat=yyyy/MM/dd}"
                                               Style="{StaticResource BodyTextStyle}"
                                               HorizontalAlignment="Center"
                                               FontFamily="{StaticResource ArabicFontBold}"
                                               Foreground="{StaticResource PrimaryBrush}"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- ملاحظات -->
                        <TextBox Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="ملاحظات (اختياري)"
                                 Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="80"
                                 VerticalContentAlignment="Top"
                                 Margin="0,0,0,24"/>

                        <!-- الأزرار -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource PrimaryButtonStyle}"
                                    Content="تجديد الباقة"
                                    Command="{Binding RenewCommand}"
                                    IsEnabled="{Binding IsNotBusy}"
                                    Margin="0,0,8,0"/>
                            <Button Style="{StaticResource SecondaryButtonStyle}"
                                    Content="طباعة"
                                    Command="{Binding PrintCommand}"
                                    IsEnabled="{Binding CanPrint}"
                                    Margin="0,0,8,0"/>
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Content="مسح"
                                    Command="{Binding ClearCommand}"
                                    IsEnabled="{Binding IsNotBusy}"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- قائمة العملاء المستحقين للتجديد -->
            <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <materialDesign:PackIcon Kind="AccountClock"
                                                 Width="24" Height="24"
                                                 Foreground="{StaticResource WarningBrush}"
                                                 Margin="0,0,8,0"/>
                        <TextBlock Text="العملاء المستحقون للتجديد"
                                   Style="{StaticResource SubHeaderStyle}"/>
                    </StackPanel>

                    <DataGrid ItemsSource="{Binding CustomersDueForRenewal}"
                              Style="{StaticResource DataGridStyle}"
                              MaxHeight="400"
                              SelectedItem="{Binding SelectedCustomerFromList}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم العميل" 
                                                Binding="{Binding FullName}"
                                                Width="*"/>
                            <DataGridTextColumn Header="تاريخ الانتهاء" 
                                                Binding="{Binding PackageEndDate, StringFormat=MM/dd}"
                                                Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                            Content="تحديث القائمة"
                            Command="{Binding RefreshCommand}"
                            HorizontalAlignment="Center"
                            Margin="0,16,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Page>