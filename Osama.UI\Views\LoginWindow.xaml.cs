using Osama.ViewModels;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Osama.UI.Views
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// </summary>
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;

        public LoginWindow()
        {
            InitializeComponent();
            
            // الحصول على ViewModel من DI Container
            _viewModel = App.GetService<LoginViewModel>();
            DataContext = _viewModel;

            // ربط أحداث ViewModel
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;

            // التركيز على حقل اسم المستخدم
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        /// <summary>
        /// معالج تغيير خصائص ViewModel
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(LoginViewModel.IsLoginSuccessful) && _viewModel.IsLoginSuccessful)
            {
                // فتح النافذة الرئيسية
                var mainWindow = App.GetService<MainWindow>();
                mainWindow.Show();
                
                // إغلاق نافذة تسجيل الدخول
                Close();
            }
        }

        /// <summary>
        /// معالج تغيير كلمة المرور
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.Password = passwordBox.Password;
            }
        }

        /// <summary>
        /// معالج زر الإغلاق
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        /// <summary>
        /// معالج الضغط على Enter في حقول الإدخال
        /// </summary>
        private void InputField_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && _viewModel.LoginCommand.CanExecute(null))
            {
                _viewModel.LoginCommand.Execute(null);
            }
        }

        /// <summary>
        /// معالج سحب النافذة
        /// </summary>
        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            // إلغاء ربط الأحداث
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
            base.OnClosed(e);
        }
    }
}