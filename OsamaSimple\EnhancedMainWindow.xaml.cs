using Osama.Services;
using System;
using System.Windows;
using System.Windows.Threading;

namespace OsamaSimple
{
    public partial class EnhancedMainWindow : Window
    {
        private readonly DispatcherTimer _timer;

        public EnhancedMainWindow()
        {
            InitializeComponent();
            
            // إعداد المؤقت لتحديث الوقت
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            
            // تحديث معلومات المستخدم
            UpdateUserInfo();
            
            // تحميل الصفحة الرئيسية
            LoadDashboard();
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            DateTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
        }

        private void UpdateUserInfo()
        {
            try
            {
                var authService = App.GetService<AuthenticationService>();
                var currentUser = authService.CurrentUser;

                if (currentUser != null)
                {
                    UserInfoText.Text = $"مرحباً، {currentUser.FullName}";
                }
                else
                {
                    UserInfoText.Text = "مستخدم غير معروف";
                }
            }
            catch (Exception)
            {
                UserInfoText.Text = "مستخدم غير معروف";
            }
        }

        private void LoadDashboard()
        {
            try
            {
                // تحميل صفحة لوحة التحكم
                StatusText.Text = "تم تحميل لوحة التحكم";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        // معالجات أحداث القائمة
        private void NewSubscription_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "اشتراك جديد";
            MessageBox.Show("سيتم تحميل صفحة الاشتراك الجديد", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PackageRenewal_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "تجديد باقة";
            MessageBox.Show("سيتم تحميل صفحة تجديد الباقة", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RouterDelivery_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "توصيل راوتر";
            MessageBox.Show("سيتم تحميل صفحة توصيل الراوتر", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ManageProducts_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "إدارة المنتجات";
            MessageBox.Show("سيتم تحميل صفحة إدارة المنتجات", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void InventoryMovement_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "حركة المخزون";
            MessageBox.Show("سيتم تحميل صفحة حركة المخزون", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ManageCustomers_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "إدارة العملاء";
            MessageBox.Show("سيتم تحميل صفحة إدارة العملاء", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ManageDistributors_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "إدارة الموزعين";
            MessageBox.Show("سيتم تحميل صفحة إدارة الموزعين", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ManageTreasury_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "إدارة الخزينة";
            MessageBox.Show("سيتم تحميل صفحة إدارة الخزينة", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ManageExpenses_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "المصروفات";
            MessageBox.Show("سيتم تحميل صفحة المصروفات", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SalesReport_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "تقرير المبيعات";
            MessageBox.Show("سيتم تحميل تقرير المبيعات", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void InventoryReport_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "تقرير المخزون";
            MessageBox.Show("سيتم تحميل تقرير المخزون", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
