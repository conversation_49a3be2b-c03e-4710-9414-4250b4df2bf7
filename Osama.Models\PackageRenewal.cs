using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج تجديد الباقة
    /// </summary>
    public class PackageRenewal
    {
        [Key]
        public int RenewalId { get; set; }

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public int PackageId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public DateTime RenewalDate { get; set; } = DateTime.Now;

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public int ShiftId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual Customer Customer { get; set; } = null!;
        public virtual Package Package { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual Shift Shift { get; set; } = null!;
    }
}