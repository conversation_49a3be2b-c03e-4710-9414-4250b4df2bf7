using Microsoft.EntityFrameworkCore.Storage;
using Osama.Models;

namespace Osama.Data.Repositories
{
    /// <summary>
    /// تنفيذ وحدة العمل
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly OsamaDbContext _context;
        private IDbContextTransaction? _transaction;

        // المستودعات
        private IRepository<User>? _users;
        private IRepository<Customer>? _customers;
        private IRepository<Product>? _products;
        private IRepository<Package>? _packages;
        private IRepository<Supplier>? _suppliers;
        private IRepository<Distributor>? _distributors;
        private IRepository<Sale>? _sales;
        private IRepository<RouterDelivery>? _routerDeliveries;
        private IRepository<PackageRenewal>? _packageRenewals;
        private IRepository<Shift>? _shifts;
        private IRepository<Purchase>? _purchases;
        private IRepository<PurchaseItem>? _purchaseItems;
        private IRepository<Expense>? _expenses;
        private IRepository<Treasury>? _treasuries;
        private IRepository<TreasuryTransaction>? _treasuryTransactions;
        private IRepository<CashboxTransaction>? _cashboxTransactions;
        private IRepository<ReceiptVoucher>? _receiptVouchers;
        private IRepository<PaymentVoucher>? _paymentVouchers;
        private IRepository<BalanceRecharge>? _balanceRecharges;
        private IRepository<InventoryTransaction>? _inventoryTransactions;
        private IRepository<WorkerInventory>? _workerInventories;
        private IRepository<WorkerInventoryItem>? _workerInventoryItems;
        private IRepository<MaintenanceOrder>? _maintenanceOrders;
        private IRepository<MaintenanceOrderItem>? _maintenanceOrderItems;
        private IRepository<Settings>? _settings;

        public UnitOfWork(OsamaDbContext context)
        {
            _context = context;
        }

        // خصائص المستودعات
        public IRepository<User> Users => _users ??= new Repository<User>(_context);
        public IRepository<Customer> Customers => _customers ??= new Repository<Customer>(_context);
        public IRepository<Product> Products => _products ??= new Repository<Product>(_context);
        public IRepository<Package> Packages => _packages ??= new Repository<Package>(_context);
        public IRepository<Supplier> Suppliers => _suppliers ??= new Repository<Supplier>(_context);
        public IRepository<Distributor> Distributors => _distributors ??= new Repository<Distributor>(_context);
        public IRepository<Sale> Sales => _sales ??= new Repository<Sale>(_context);
        public IRepository<RouterDelivery> RouterDeliveries => _routerDeliveries ??= new Repository<RouterDelivery>(_context);
        public IRepository<PackageRenewal> PackageRenewals => _packageRenewals ??= new Repository<PackageRenewal>(_context);
        public IRepository<Shift> Shifts => _shifts ??= new Repository<Shift>(_context);
        public IRepository<Purchase> Purchases => _purchases ??= new Repository<Purchase>(_context);
        public IRepository<PurchaseItem> PurchaseItems => _purchaseItems ??= new Repository<PurchaseItem>(_context);
        public IRepository<Expense> Expenses => _expenses ??= new Repository<Expense>(_context);
        public IRepository<Treasury> Treasuries => _treasuries ??= new Repository<Treasury>(_context);
        public IRepository<TreasuryTransaction> TreasuryTransactions => _treasuryTransactions ??= new Repository<TreasuryTransaction>(_context);
        public IRepository<CashboxTransaction> CashboxTransactions => _cashboxTransactions ??= new Repository<CashboxTransaction>(_context);
        public IRepository<ReceiptVoucher> ReceiptVouchers => _receiptVouchers ??= new Repository<ReceiptVoucher>(_context);
        public IRepository<PaymentVoucher> PaymentVouchers => _paymentVouchers ??= new Repository<PaymentVoucher>(_context);
        public IRepository<BalanceRecharge> BalanceRecharges => _balanceRecharges ??= new Repository<BalanceRecharge>(_context);
        public IRepository<InventoryTransaction> InventoryTransactions => _inventoryTransactions ??= new Repository<InventoryTransaction>(_context);
        public IRepository<WorkerInventory> WorkerInventories => _workerInventories ??= new Repository<WorkerInventory>(_context);
        public IRepository<WorkerInventoryItem> WorkerInventoryItems => _workerInventoryItems ??= new Repository<WorkerInventoryItem>(_context);
        public IRepository<MaintenanceOrder> MaintenanceOrders => _maintenanceOrders ??= new Repository<MaintenanceOrder>(_context);
        public IRepository<MaintenanceOrderItem> MaintenanceOrderItems => _maintenanceOrderItems ??= new Repository<MaintenanceOrderItem>(_context);
        public IRepository<Settings> Settings => _settings ??= new Repository<Settings>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}