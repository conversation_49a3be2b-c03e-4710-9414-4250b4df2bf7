using Osama.Services;
using System;
using System.Windows;

namespace OsamaSimple
{
    public partial class LoginWindow : Window
    {
        private readonly AuthenticationService? _authService;

        public LoginWindow()
        {
            InitializeComponent();
            UsernameTextBox.Focus();

            try
            {
                _authService = App.GetService<AuthenticationService>();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة خدمة المصادقة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoginButton.IsEnabled = false;
                ErrorMessage.Visibility = Visibility.Collapsed;

                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ErrorMessage.Text = "يرجى إدخال اسم المستخدم وكلمة المرور";
                    ErrorMessage.Visibility = Visibility.Visible;
                    return;
                }

                // محاولة تسجيل الدخول باستخدام خدمة المصادقة
                if (_authService == null)
                {
                    ErrorMessage.Text = "خدمة المصادقة غير متاحة";
                    ErrorMessage.Visibility = Visibility.Visible;
                    return;
                }

                var loginResult = await _authService.LoginAsync(username, password);

                if (loginResult.Success && loginResult.User != null)
                {
                    MessageBox.Show($"مرحباً {loginResult.User.FullName}!", "تم تسجيل الدخول بنجاح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // فتح النافذة الرئيسية المحسنة
                    var mainWindow = new EnhancedMainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ErrorMessage.Text = loginResult.Message;
                    ErrorMessage.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                ErrorMessage.Text = $"خطأ في تسجيل الدخول: {ex.Message}";
                ErrorMessage.Visibility = Visibility.Visible;
            }
            finally
            {
                LoginButton.IsEnabled = true;
            }
        }
    }
}
