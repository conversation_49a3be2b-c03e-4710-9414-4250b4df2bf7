using System.Windows;

namespace OsamaSimple
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
            UsernameTextBox.Focus();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            string username = UsernameTextBox.Text;
            string password = PasswordBox.Password;

            // تسجيل دخول مبسط للاختبار
            if (username == "admin" && password == "admin")
            {
                MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // فتح النافذة الرئيسية
                var mainWindow = new MainWindow();
                mainWindow.Show();
                this.Close();
            }
            else
            {
                ErrorMessage.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                ErrorMessage.Visibility = Visibility.Visible;
            }
        }
    }
}
