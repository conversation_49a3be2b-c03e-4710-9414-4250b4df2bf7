﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Osama.Data;

#nullable disable

namespace Osama.DbMigrations.Migrations
{
    [DbContext(typeof(OsamaDbContext))]
    [Migration("20250808094939_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "7.0.0");

            modelBuilder.Entity("Osama.Models.BalanceRecharge", b =>
                {
                    b.Property<int>("RechargeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("DistributorId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("RechargeDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("RechargeId");

                    b.HasIndex("DistributorId");

                    b.HasIndex("UserId");

                    b.ToTable("BalanceRecharges");
                });

            modelBuilder.Entity("Osama.Models.CashboxTransaction", b =>
                {
                    b.Property<int>("TransactionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("TransactionId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("UserId");

                    b.ToTable("CashboxTransactions");
                });

            modelBuilder.Entity("Osama.Models.Customer", b =>
                {
                    b.Property<int>("CustomerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CurrentPackageId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("NationalId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PackageEndDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PackageStartDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("RegistrationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("GETDATE()");

                    b.HasKey("CustomerId");

                    b.HasIndex("CurrentPackageId");

                    b.HasIndex("FullName");

                    b.HasIndex("PhoneNumber");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Osama.Models.Distributor", b =>
                {
                    b.Property<int>("DistributorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("CurrentBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.HasKey("DistributorId");

                    b.ToTable("Distributors");
                });

            modelBuilder.Entity("Osama.Models.Expense", b =>
                {
                    b.Property<int>("ExpenseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Category")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpenseDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ReceiptNumber")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("ExpenseId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("UserId");

                    b.ToTable("Expenses");
                });

            modelBuilder.Entity("Osama.Models.InventoryTransaction", b =>
                {
                    b.Property<int>("TransactionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ReferenceId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("TransactionId");

                    b.HasIndex("ProductId");

                    b.HasIndex("UserId");

                    b.ToTable("InventoryTransactions");
                });

            modelBuilder.Entity("Osama.Models.MaintenanceOrder", b =>
                {
                    b.Property<int>("OrderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("OrderId");

                    b.HasIndex("UserId");

                    b.ToTable("MaintenanceOrders");
                });

            modelBuilder.Entity("Osama.Models.MaintenanceOrderItem", b =>
                {
                    b.Property<int>("ItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("OrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.HasKey("ItemId");

                    b.HasIndex("OrderId");

                    b.HasIndex("ProductId");

                    b.ToTable("MaintenanceOrderItems");
                });

            modelBuilder.Entity("Osama.Models.Package", b =>
                {
                    b.Property<int>("PackageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("DurationDays")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SpeedMbps")
                        .HasColumnType("INTEGER");

                    b.HasKey("PackageId");

                    b.ToTable("Packages");

                    b.HasData(
                        new
                        {
                            PackageId = 1,
                            CreatedDate = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8712),
                            Description = "باقة إنترنت أساسية",
                            DurationDays = 30,
                            IsActive = true,
                            Name = "باقة أساسية",
                            Price = 25000m,
                            SpeedMbps = 10
                        },
                        new
                        {
                            PackageId = 2,
                            CreatedDate = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8788),
                            Description = "باقة إنترنت متوسطة",
                            DurationDays = 30,
                            IsActive = true,
                            Name = "باقة متوسطة",
                            Price = 40000m,
                            SpeedMbps = 20
                        },
                        new
                        {
                            PackageId = 3,
                            CreatedDate = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8793),
                            Description = "باقة إنترنت متقدمة",
                            DurationDays = 30,
                            IsActive = true,
                            Name = "باقة متقدمة",
                            Price = 60000m,
                            SpeedMbps = 50
                        });
                });

            modelBuilder.Entity("Osama.Models.PackageRenewal", b =>
                {
                    b.Property<int>("RenewalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("PackageId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("RenewalDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("RenewalId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("PackageId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("UserId");

                    b.ToTable("PackageRenewals");
                });

            modelBuilder.Entity("Osama.Models.PaymentVoucher", b =>
                {
                    b.Property<int>("VoucherId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("VoucherDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("VoucherNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("VoucherId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("UserId");

                    b.ToTable("PaymentVouchers");
                });

            modelBuilder.Entity("Osama.Models.Product", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Brand")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("Category")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<int>("CurrentStock")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MinimumStock")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Model")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("ProductId");

                    b.HasIndex("Name");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Osama.Models.Purchase", b =>
                {
                    b.Property<int>("PurchaseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("PurchaseDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("RemainingAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SupplierId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("PurchaseId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("UserId");

                    b.ToTable("Purchases");
                });

            modelBuilder.Entity("Osama.Models.PurchaseItem", b =>
                {
                    b.Property<int>("PurchaseItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PurchaseId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("PurchaseItemId");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseId");

                    b.ToTable("PurchaseItems");
                });

            modelBuilder.Entity("Osama.Models.ReceiptVoucher", b =>
                {
                    b.Property<int>("VoucherId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("DistributorId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("VoucherDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("VoucherNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("VoucherId");

                    b.HasIndex("DistributorId");

                    b.HasIndex("UserId");

                    b.ToTable("ReceiptVouchers");
                });

            modelBuilder.Entity("Osama.Models.RouterDelivery", b =>
                {
                    b.Property<int>("DeliveryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CableProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DeliveryDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("InstallerId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("PackageId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("PackagePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("RouterProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("SubscriptionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("DeliveryId");

                    b.HasIndex("CableProductId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("InstallerId");

                    b.HasIndex("PackageId");

                    b.HasIndex("RouterProductId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("UserId");

                    b.ToTable("RouterDeliveries");
                });

            modelBuilder.Entity("Osama.Models.Sale", b =>
                {
                    b.Property<int>("SaleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("RouterPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("RouterProductId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("SaleDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("SubscriptionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("SaleId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("RouterProductId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("UserId");

                    b.ToTable("Sales");
                });

            modelBuilder.Entity("Osama.Models.Settings", b =>
                {
                    b.Property<int>("SettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("SettingId");

                    b.ToTable("Settings");

                    b.HasData(
                        new
                        {
                            SettingId = 1,
                            Description = "رسم الاشتراك الافتراضي",
                            Key = "SubscriptionFee",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8578),
                            Value = "50000"
                        },
                        new
                        {
                            SettingId = 2,
                            Description = "معدل الضريبة",
                            Key = "TaxRate",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8582),
                            Value = "0"
                        },
                        new
                        {
                            SettingId = 3,
                            Description = "العملة الافتراضية",
                            Key = "Currency",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8586),
                            Value = "ليرة سورية"
                        },
                        new
                        {
                            SettingId = 4,
                            Description = "اسم الشركة",
                            Key = "CompanyName",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8591),
                            Value = "شركة أسامة للإنترنت"
                        },
                        new
                        {
                            SettingId = 5,
                            Description = "عنوان الشركة",
                            Key = "CompanyAddress",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8619),
                            Value = "دمشق - سوريا"
                        },
                        new
                        {
                            SettingId = 6,
                            Description = "هاتف الشركة",
                            Key = "CompanyPhone",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8622),
                            Value = "011-1234567"
                        },
                        new
                        {
                            SettingId = 7,
                            Description = "سعر صرف الدولار",
                            Key = "DollarExchangeRate",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8625),
                            Value = "15000"
                        },
                        new
                        {
                            SettingId = 8,
                            Description = "حد التنبيه للمخزون المنخفض",
                            Key = "MinimumStockAlert",
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8637),
                            Value = "5"
                        });
                });

            modelBuilder.Entity("Osama.Models.Shift", b =>
                {
                    b.Property<int>("ShiftId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("ClosingBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("OpeningBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalExpenses")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalSales")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("ShiftId");

                    b.HasIndex("UserId");

                    b.ToTable("Shifts");
                });

            modelBuilder.Entity("Osama.Models.Supplier", b =>
                {
                    b.Property<int>("SupplierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.HasKey("SupplierId");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("Osama.Models.Treasury", b =>
                {
                    b.Property<int>("TreasuryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DollarBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SyrianPoundBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("TreasuryId");

                    b.ToTable("Treasuries");

                    b.HasData(
                        new
                        {
                            TreasuryId = 1,
                            CreatedDate = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8461),
                            DollarBalance = 0m,
                            IsActive = true,
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8467),
                            Name = "الخزينة اليومية",
                            SyrianPoundBalance = 0m,
                            Type = 1
                        },
                        new
                        {
                            TreasuryId = 2,
                            CreatedDate = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8507),
                            DollarBalance = 0m,
                            IsActive = true,
                            LastUpdated = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(8513),
                            Name = "الخزينة الرئيسية",
                            SyrianPoundBalance = 0m,
                            Type = 2
                        });
                });

            modelBuilder.Entity("Osama.Models.TreasuryTransaction", b =>
                {
                    b.Property<int>("TransactionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DollarAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SyrianPoundAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("TreasuryId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("TransactionId");

                    b.HasIndex("TreasuryId");

                    b.HasIndex("UserId");

                    b.ToTable("TreasuryTransactions");
                });

            modelBuilder.Entity("Osama.Models.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<int>("Role")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("UserId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            UserId = 1,
                            CreatedDate = new DateTime(2025, 8, 8, 12, 49, 38, 726, DateTimeKind.Local).AddTicks(7551),
                            FullName = "مدير النظام",
                            IsActive = true,
                            PasswordHash = "$2a$11$vTKcUr/soKw9KieHVg4kCOE/BvBVcyCFhqDYiRBznnjtJ8PhuLAXi",
                            Role = 1,
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("Osama.Models.WorkerInventory", b =>
                {
                    b.Property<int>("WorkerInventoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("WorkerInventoryId");

                    b.HasIndex("UserId");

                    b.ToTable("WorkerInventories");
                });

            modelBuilder.Entity("Osama.Models.WorkerInventoryItem", b =>
                {
                    b.Property<int>("ItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<int>("WorkerInventoryId")
                        .HasColumnType("INTEGER");

                    b.HasKey("ItemId");

                    b.HasIndex("ProductId");

                    b.HasIndex("WorkerInventoryId");

                    b.ToTable("WorkerInventoryItems");
                });

            modelBuilder.Entity("Osama.Models.BalanceRecharge", b =>
                {
                    b.HasOne("Osama.Models.Distributor", "Distributor")
                        .WithMany("BalanceRecharges")
                        .HasForeignKey("DistributorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Distributor");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.CashboxTransaction", b =>
                {
                    b.HasOne("Osama.Models.Shift", "Shift")
                        .WithMany("CashboxTransactions")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany("CashboxTransactions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shift");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.Customer", b =>
                {
                    b.HasOne("Osama.Models.Package", "CurrentPackage")
                        .WithMany("Customers")
                        .HasForeignKey("CurrentPackageId");

                    b.Navigation("CurrentPackage");
                });

            modelBuilder.Entity("Osama.Models.Expense", b =>
                {
                    b.HasOne("Osama.Models.Shift", "Shift")
                        .WithMany("Expenses")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany("Expenses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shift");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.InventoryTransaction", b =>
                {
                    b.HasOne("Osama.Models.Product", "Product")
                        .WithMany("InventoryTransactions")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.MaintenanceOrder", b =>
                {
                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.MaintenanceOrderItem", b =>
                {
                    b.HasOne("Osama.Models.MaintenanceOrder", "Order")
                        .WithMany("Items")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Osama.Models.PackageRenewal", b =>
                {
                    b.HasOne("Osama.Models.Customer", "Customer")
                        .WithMany("PackageRenewals")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.Package", "Package")
                        .WithMany("PackageRenewals")
                        .HasForeignKey("PackageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.Shift", "Shift")
                        .WithMany("PackageRenewals")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Package");

                    b.Navigation("Shift");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.PaymentVoucher", b =>
                {
                    b.HasOne("Osama.Models.Supplier", "Supplier")
                        .WithMany("PaymentVouchers")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.Purchase", b =>
                {
                    b.HasOne("Osama.Models.Supplier", "Supplier")
                        .WithMany("Purchases")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.PurchaseItem", b =>
                {
                    b.HasOne("Osama.Models.Product", "Product")
                        .WithMany("PurchaseItems")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.Purchase", "Purchase")
                        .WithMany("PurchaseItems")
                        .HasForeignKey("PurchaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Purchase");
                });

            modelBuilder.Entity("Osama.Models.ReceiptVoucher", b =>
                {
                    b.HasOne("Osama.Models.Distributor", "Distributor")
                        .WithMany("ReceiptVouchers")
                        .HasForeignKey("DistributorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Distributor");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.RouterDelivery", b =>
                {
                    b.HasOne("Osama.Models.Product", "CableProduct")
                        .WithMany()
                        .HasForeignKey("CableProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Osama.Models.Customer", "Customer")
                        .WithMany("RouterDeliveries")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "Installer")
                        .WithMany()
                        .HasForeignKey("InstallerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Osama.Models.Package", "Package")
                        .WithMany("RouterDeliveries")
                        .HasForeignKey("PackageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.Product", "RouterProduct")
                        .WithMany()
                        .HasForeignKey("RouterProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Osama.Models.Shift", "Shift")
                        .WithMany("RouterDeliveries")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CableProduct");

                    b.Navigation("Customer");

                    b.Navigation("Installer");

                    b.Navigation("Package");

                    b.Navigation("RouterProduct");

                    b.Navigation("Shift");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.Sale", b =>
                {
                    b.HasOne("Osama.Models.Customer", "Customer")
                        .WithMany("Sales")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.Product", "RouterProduct")
                        .WithMany()
                        .HasForeignKey("RouterProductId");

                    b.HasOne("Osama.Models.Shift", "Shift")
                        .WithMany("Sales")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany("Sales")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("RouterProduct");

                    b.Navigation("Shift");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.Shift", b =>
                {
                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.TreasuryTransaction", b =>
                {
                    b.HasOne("Osama.Models.Treasury", "Treasury")
                        .WithMany("TreasuryTransactions")
                        .HasForeignKey("TreasuryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Treasury");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.WorkerInventory", b =>
                {
                    b.HasOne("Osama.Models.User", "User")
                        .WithMany("WorkerInventories")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Osama.Models.WorkerInventoryItem", b =>
                {
                    b.HasOne("Osama.Models.Product", "Product")
                        .WithMany("WorkerInventoryItems")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Osama.Models.WorkerInventory", "WorkerInventory")
                        .WithMany("Items")
                        .HasForeignKey("WorkerInventoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("WorkerInventory");
                });

            modelBuilder.Entity("Osama.Models.Customer", b =>
                {
                    b.Navigation("PackageRenewals");

                    b.Navigation("RouterDeliveries");

                    b.Navigation("Sales");
                });

            modelBuilder.Entity("Osama.Models.Distributor", b =>
                {
                    b.Navigation("BalanceRecharges");

                    b.Navigation("ReceiptVouchers");
                });

            modelBuilder.Entity("Osama.Models.MaintenanceOrder", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("Osama.Models.Package", b =>
                {
                    b.Navigation("Customers");

                    b.Navigation("PackageRenewals");

                    b.Navigation("RouterDeliveries");
                });

            modelBuilder.Entity("Osama.Models.Product", b =>
                {
                    b.Navigation("InventoryTransactions");

                    b.Navigation("PurchaseItems");

                    b.Navigation("WorkerInventoryItems");
                });

            modelBuilder.Entity("Osama.Models.Purchase", b =>
                {
                    b.Navigation("PurchaseItems");
                });

            modelBuilder.Entity("Osama.Models.Shift", b =>
                {
                    b.Navigation("CashboxTransactions");

                    b.Navigation("Expenses");

                    b.Navigation("PackageRenewals");

                    b.Navigation("RouterDeliveries");

                    b.Navigation("Sales");
                });

            modelBuilder.Entity("Osama.Models.Supplier", b =>
                {
                    b.Navigation("PaymentVouchers");

                    b.Navigation("Purchases");
                });

            modelBuilder.Entity("Osama.Models.Treasury", b =>
                {
                    b.Navigation("TreasuryTransactions");
                });

            modelBuilder.Entity("Osama.Models.User", b =>
                {
                    b.Navigation("CashboxTransactions");

                    b.Navigation("Expenses");

                    b.Navigation("Sales");

                    b.Navigation("WorkerInventories");
                });

            modelBuilder.Entity("Osama.Models.WorkerInventory", b =>
                {
                    b.Navigation("Items");
                });
#pragma warning restore 612, 618
        }
    }
}
