using Osama.Data.Repositories;
using Osama.Models;

namespace Osama.Services
{
    /// <summary>
    /// خدمة إدارة المخزون
    /// </summary>
    public class InventoryService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AuthenticationService _authService;

        public InventoryService(IUnitOfWork unitOfWork, AuthenticationService authService)
        {
            _unitOfWork = unitOfWork;
            _authService = authService;
        }

        /// <summary>
        /// إضافة معاملة مخزون
        /// </summary>
        /// <param name="productId">معرف المنتج</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="type">نوع المعاملة</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="unitPrice">سعر الوحدة</param>
        /// <param name="description">الوصف</param>
        /// <param name="referenceId">المرجع</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>معاملة المخزون</returns>
        public async Task<InventoryTransaction?> AddInventoryTransactionAsync(int productId, int userId, InventoryTransactionType type, int quantity, decimal unitPrice, string description, int? referenceId = null, string? notes = null)
        {
            try
            {
                var product = await _unitOfWork.Products.GetByIdAsync(productId);
                if (product == null)
                    return null;

                // إنشاء المعاملة
                var transaction = new InventoryTransaction
                {
                    ProductId = productId,
                    UserId = userId,
                    Type = type,
                    Quantity = quantity,
                    UnitPrice = unitPrice,
                    Description = description,
                    TransactionDate = DateTime.Now,
                    Notes = notes,
                    ReferenceId = referenceId
                };

                await _unitOfWork.InventoryTransactions.AddAsync(transaction);

                // تحديث المخزون
                switch (type)
                {
                    case InventoryTransactionType.إضافة:
                        product.CurrentStock += quantity;
                        break;
                    case InventoryTransactionType.سحب:
                    case InventoryTransactionType.تسليم_عامل:
                    case InventoryTransactionType.صيانة:
                    case InventoryTransactionType.تلف:
                        product.CurrentStock -= quantity;
                        break;
                    case InventoryTransactionType.إرجاع_عامل:
                        product.CurrentStock += quantity;
                        break;
                    case InventoryTransactionType.جرد:
                        product.CurrentStock = quantity; // في حالة الجرد، الكمية هي الرصيد الجديد
                        break;
                }

                await _unitOfWork.Products.UpdateAsync(product);
                return transaction;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// تسليم منتجات للعامل
        /// </summary>
        /// <param name="workerId">معرف العامل</param>
        /// <param name="items">قائمة المنتجات والكميات</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<bool> DeliverToWorkerAsync(int workerId, List<(int ProductId, int Quantity)> items, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return false;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // الحصول على مخزون العامل أو إنشاؤه
                var workerInventory = await _unitOfWork.WorkerInventories.FirstOrDefaultAsync(wi => wi.UserId == workerId && wi.IsActive);
                if (workerInventory == null)
                {
                    workerInventory = new WorkerInventory
                    {
                        UserId = workerId,
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };
                    await _unitOfWork.WorkerInventories.AddAsync(workerInventory);
                }

                foreach (var (productId, quantity) in items)
                {
                    var product = await _unitOfWork.Products.GetByIdAsync(productId);
                    if (product == null || product.CurrentStock < quantity)
                        continue;

                    // إضافة معاملة مخزون
                    await AddInventoryTransactionAsync(productId, _authService.CurrentUser.UserId, InventoryTransactionType.تسليم_عامل, quantity, product.PurchasePrice, $"تسليم للعامل", workerId, notes);

                    // تحديث مخزون العامل
                    var workerItem = await _unitOfWork.WorkerInventoryItems.FirstOrDefaultAsync(wii => wii.WorkerInventoryId == workerInventory.WorkerInventoryId && wii.ProductId == productId);
                    if (workerItem == null)
                    {
                        workerItem = new WorkerInventoryItem
                        {
                            WorkerInventoryId = workerInventory.WorkerInventoryId,
                            ProductId = productId,
                            Quantity = quantity,
                            LastUpdated = DateTime.Now
                        };
                        await _unitOfWork.WorkerInventoryItems.AddAsync(workerItem);
                    }
                    else
                    {
                        workerItem.Quantity += quantity;
                        workerItem.LastUpdated = DateTime.Now;
                        await _unitOfWork.WorkerInventoryItems.UpdateAsync(workerItem);
                    }
                }

                await _unitOfWork.CommitTransactionAsync();
                return true;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return false;
            }
        }

        /// <summary>
        /// إنشاء أمر صيانة
        /// </summary>
        /// <param name="orderNumber">رقم الأمر</param>
        /// <param name="description">الوصف</param>
        /// <param name="items">قائمة المنتجات والكميات</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>أمر الصيانة</returns>
        public async Task<MaintenanceOrder?> CreateMaintenanceOrderAsync(string orderNumber, string description, List<(int ProductId, int Quantity, string? Notes)> items, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return null;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // إنشاء أمر الصيانة
                var order = new MaintenanceOrder
                {
                    OrderNumber = orderNumber,
                    UserId = _authService.CurrentUser.UserId,
                    Description = description,
                    OrderDate = DateTime.Now,
                    Notes = notes
                };

                await _unitOfWork.MaintenanceOrders.AddAsync(order);

                // إضافة عناصر الأمر
                foreach (var (productId, quantity, itemNotes) in items)
                {
                    var product = await _unitOfWork.Products.GetByIdAsync(productId);
                    if (product == null || product.CurrentStock < quantity)
                        continue;

                    var orderItem = new MaintenanceOrderItem
                    {
                        OrderId = order.OrderId,
                        ProductId = productId,
                        Quantity = quantity,
                        Notes = itemNotes
                    };

                    await _unitOfWork.MaintenanceOrderItems.AddAsync(orderItem);

                    // إضافة معاملة مخزون
                    await AddInventoryTransactionAsync(productId, _authService.CurrentUser.UserId, InventoryTransactionType.صيانة, quantity, product.PurchasePrice, $"أمر صيانة رقم {orderNumber}", order.OrderId);
                }

                await _unitOfWork.CommitTransactionAsync();
                return order;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return null;
            }
        }

        /// <summary>
        /// الحصول على المنتجات منخفضة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات</returns>
        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _unitOfWork.Products.FindAsync(p => p.CurrentStock <= p.MinimumStock && p.IsActive);
        }

        /// <summary>
        /// الحصول على تقرير حركة المخزون
        /// </summary>
        /// <param name="productId">معرف المنتج (اختياري)</param>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة المعاملات</returns>
        public async Task<IEnumerable<InventoryTransaction>> GetInventoryMovementReportAsync(int? productId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = await _unitOfWork.InventoryTransactions.GetAllWithIncludeAsync(
                it => it.Product,
                it => it.User
            );

            if (productId.HasValue)
                query = query.Where(it => it.ProductId == productId.Value);

            if (startDate.HasValue)
                query = query.Where(it => it.TransactionDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(it => it.TransactionDate <= endDate.Value);

            return query.OrderByDescending(it => it.TransactionDate);
        }

        /// <summary>
        /// الحصول على مخزون العامل
        /// </summary>
        /// <param name="workerId">معرف العامل</param>
        /// <returns>مخزون العامل</returns>
        public async Task<IEnumerable<WorkerInventoryItem>> GetWorkerInventoryAsync(int workerId)
        {
            var workerInventory = await _unitOfWork.WorkerInventories.FirstOrDefaultAsync(wi => wi.UserId == workerId && wi.IsActive);
            if (workerInventory == null)
                return new List<WorkerInventoryItem>();

            return await _unitOfWork.WorkerInventoryItems.FindWithIncludeAsync(
                wii => wii.WorkerInventoryId == workerInventory.WorkerInventoryId && wii.Quantity > 0,
                wii => wii.Product
            );
        }

        /// <summary>
        /// تقرير استهلاك الكابلات حسب العامل
        /// </summary>
        /// <param name="workerId">معرف العامل (اختياري)</param>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>تقرير الاستهلاك</returns>
        public async Task<IEnumerable<CableConsumptionReport>> GetCableConsumptionReportAsync(int? workerId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var deliveries = await _unitOfWork.RouterDeliveries.GetAllWithIncludeAsync(
                rd => rd.Installer,
                rd => rd.CableProduct
            );

            var query = deliveries.Where(rd => rd.CableProduct.Category == ProductCategory.كابل);

            if (workerId.HasValue)
                query = query.Where(rd => rd.InstallerId == workerId.Value);

            if (startDate.HasValue)
                query = query.Where(rd => rd.DeliveryDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(rd => rd.DeliveryDate <= endDate.Value);

            return query.GroupBy(rd => new { rd.InstallerId, rd.Installer.FullName, rd.CableProductId, rd.CableProduct.Name })
                       .Select(g => new CableConsumptionReport
                       {
                           WorkerId = g.Key.InstallerId,
                           WorkerName = g.Key.FullName,
                           CableProductId = g.Key.CableProductId,
                           CableProductName = g.Key.Name,
                           TotalQuantity = g.Count(),
                           TotalCost = g.Sum(rd => rd.CableProduct.PurchasePrice)
                       });
        }
    }

    /// <summary>
    /// تقرير استهلاك الكابلات
    /// </summary>
    public class CableConsumptionReport
    {
        public int WorkerId { get; set; }
        public string WorkerName { get; set; } = string.Empty;
        public int CableProductId { get; set; }
        public string CableProductName { get; set; } = string.Empty;
        public int TotalQuantity { get; set; }
        public decimal TotalCost { get; set; }
    }
}