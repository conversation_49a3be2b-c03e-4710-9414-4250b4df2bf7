<Window x:Class="OsamaSimple.EnhancedMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام أسامة - النافذة الرئيسية" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid>
                <TextBlock Text="نظام أسامة - إدارة شركات الإنترنت" 
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           Foreground="White"
                           FontSize="20"
                           FontWeight="Bold"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="20,0">
                    <Button Content="تسجيل الخروج"
                            Padding="15,5"
                            Background="#F44336"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,10,0"
                            Click="LogoutButton_Click"/>
                    
                    <TextBlock x:Name="UserInfoText"
                               VerticalAlignment="Center"
                               Foreground="White"
                               FontSize="14"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- القائمة الجانبية -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        
                        <!-- قسم المبيعات -->
                        <Expander Header="المبيعات" IsExpanded="True" Margin="0,5">
                            <StackPanel Margin="10,5">
                                <Button Content="اشتراك جديد" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="NewSubscription_Click"/>
                                <Button Content="تجديد باقة" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="PackageRenewal_Click"/>
                                <Button Content="توصيل راوتر" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="RouterDelivery_Click"/>
                            </StackPanel>
                        </Expander>
                        
                        <!-- قسم المخزون -->
                        <Expander Header="المخزون" Margin="0,5">
                            <StackPanel Margin="10,5">
                                <Button Content="إدارة المنتجات" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="ManageProducts_Click"/>
                                <Button Content="حركة المخزون" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="InventoryMovement_Click"/>
                            </StackPanel>
                        </Expander>
                        
                        <!-- قسم العملاء -->
                        <Expander Header="العملاء" Margin="0,5">
                            <StackPanel Margin="10,5">
                                <Button Content="إدارة العملاء" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="ManageCustomers_Click"/>
                                <Button Content="إدارة الموزعين" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="ManageDistributors_Click"/>
                            </StackPanel>
                        </Expander>
                        
                        <!-- قسم الخزينة -->
                        <Expander Header="الخزينة" Margin="0,5">
                            <StackPanel Margin="10,5">
                                <Button Content="إدارة الخزينة" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="ManageTreasury_Click"/>
                                <Button Content="المصروفات" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="ManageExpenses_Click"/>
                            </StackPanel>
                        </Expander>
                        
                        <!-- قسم التقارير -->
                        <Expander Header="التقارير" Margin="0,5">
                            <StackPanel Margin="10,5">
                                <Button Content="تقرير المبيعات" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="SalesReport_Click"/>
                                <Button Content="تقرير المخزون" 
                                        Style="{StaticResource MenuButtonStyle}"
                                        Click="InventoryReport_Click"/>
                            </StackPanel>
                        </Expander>
                        
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- منطقة المحتوى -->
            <Frame x:Name="MainFrame" 
                   Grid.Column="1" 
                   NavigationUIVisibility="Hidden"
                   Background="White"/>
        </Grid>
        
        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#E0E0E0" BorderBrush="#CCC" BorderThickness="0,1,0,0">
            <Grid>
                <TextBlock x:Name="StatusText" 
                           Text="جاهز"
                           VerticalAlignment="Center"
                           Margin="10,0"
                           FontSize="12"/>
                
                <TextBlock x:Name="DateTimeText"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Center"
                           Margin="10,0"
                           FontSize="12"/>
            </Grid>
        </Border>
    </Grid>
    
    <Window.Resources>
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
</Window>
