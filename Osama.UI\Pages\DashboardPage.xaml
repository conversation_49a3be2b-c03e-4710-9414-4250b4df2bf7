<Page x:Class="Osama.UI.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="لوحة التحكم الرئيسية"
      FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <TextBlock Grid.Row="0"
                       Text="لوحة التحكم الرئيسية"
                       Style="{StaticResource HeaderStyle}"
                       Margin="0,0,0,24"/>

            <!-- البطاقات الإحصائية -->
            <Grid Grid.Row="1" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- مبيعات اليوم -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="CashMultiple"
                                                     Width="24" Height="24"
                                                     Foreground="{StaticResource SuccessBrush}"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="مبيعات اليوم"
                                       Style="{StaticResource SubHeaderStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="{Binding TodaySales, StringFormat='{}{0:N0} ل.س'}"
                                   FontFamily="{StaticResource ArabicFontBold}"
                                   FontSize="24"
                                   Foreground="{StaticResource SuccessBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,8"/>
                        
                        <TextBlock Text="إجمالي مبيعات اليوم"
                                   Style="{StaticResource BodyTextStyle}"
                                   HorizontalAlignment="Center"
                                   FontSize="12"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- مبيعات الشهر -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="TrendingUp"
                                                     Width="24" Height="24"
                                                     Foreground="{StaticResource PrimaryBrush}"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="مبيعات الشهر"
                                       Style="{StaticResource SubHeaderStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="{Binding MonthSales, StringFormat='{}{0:N0} ل.س'}"
                                   FontFamily="{StaticResource ArabicFontBold}"
                                   FontSize="24"
                                   Foreground="{StaticResource PrimaryBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,8"/>
                        
                        <TextBlock Text="إجمالي مبيعات الشهر"
                                   Style="{StaticResource BodyTextStyle}"
                                   HorizontalAlignment="Center"
                                   FontSize="12"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- رصيد الخزينة -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="Safe"
                                                     Width="24" Height="24"
                                                     Foreground="{StaticResource InfoBrush}"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="الخزينة اليومية"
                                       Style="{StaticResource SubHeaderStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="{Binding DailyTreasuryBalance, StringFormat='{}{0:N0} ل.س'}"
                                   FontFamily="{StaticResource ArabicFontBold}"
                                   FontSize="24"
                                   Foreground="{StaticResource InfoBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,8"/>
                        
                        <TextBlock Text="الرصيد الحالي"
                                   Style="{StaticResource BodyTextStyle}"
                                   HorizontalAlignment="Center"
                                   FontSize="12"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- تنبيهات المخزون -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                     Width="24" Height="24"
                                                     Foreground="{StaticResource WarningBrush}"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="تنبيهات المخزون"
                                       Style="{StaticResource SubHeaderStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="{Binding LowStockProductsCount}"
                                   FontFamily="{StaticResource ArabicFontBold}"
                                   FontSize="24"
                                   Foreground="{StaticResource WarningBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,8"/>
                        
                        <TextBlock Text="منتج منخفض المخزون"
                                   Style="{StaticResource BodyTextStyle}"
                                   HorizontalAlignment="Center"
                                   FontSize="12"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- الأقسام الرئيسية -->
            <Grid Grid.Row="2" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- العملاء المستحقون للتجديد -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="AccountClock"
                                                     Width="24" Height="24"
                                                     Foreground="{StaticResource WarningBrush}"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="العملاء المستحقون للتجديد"
                                       Style="{StaticResource SubHeaderStyle}"/>
                        </StackPanel>

                        <DataGrid ItemsSource="{Binding CustomersDueForRenewal}"
                                  Style="{StaticResource DataGridStyle}"
                                  MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم العميل" 
                                                    Binding="{Binding FullName}"
                                                    Width="*"/>
                                <DataGridTextColumn Header="تاريخ الانتهاء" 
                                                    Binding="{Binding PackageEndDate, StringFormat=yyyy/MM/dd}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="الهاتف" 
                                                    Binding="{Binding PhoneNumber}"
                                                    Width="120"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <Button Style="{StaticResource SecondaryButtonStyle}"
                                Content="عرض الكل"
                                HorizontalAlignment="Center"
                                Margin="0,16,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- المنتجات منخفضة المخزون -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="PackageDown"
                                                     Width="24" Height="24"
                                                     Foreground="{StaticResource ErrorBrush}"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="المنتجات منخفضة المخزون"
                                       Style="{StaticResource SubHeaderStyle}"/>
                        </StackPanel>

                        <DataGrid ItemsSource="{Binding LowStockProducts}"
                                  Style="{StaticResource DataGridStyle}"
                                  MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المنتج" 
                                                    Binding="{Binding Name}"
                                                    Width="*"/>
                                <DataGridTextColumn Header="المخزون الحالي" 
                                                    Binding="{Binding CurrentStock}"
                                                    Width="100"/>
                                <DataGridTextColumn Header="الحد الأدنى" 
                                                    Binding="{Binding MinimumStock}"
                                                    Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <Button Style="{StaticResource SecondaryButtonStyle}"
                                Content="عرض الكل"
                                HorizontalAlignment="Center"
                                Margin="0,16,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- المبيعات الأخيرة -->
            <materialDesign:Card Grid.Row="3" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <materialDesign:PackIcon Kind="History"
                                                 Width="24" Height="24"
                                                 Foreground="{StaticResource PrimaryBrush}"
                                                 Margin="0,0,8,0"/>
                        <TextBlock Text="المبيعات الأخيرة"
                                   Style="{StaticResource SubHeaderStyle}"/>
                    </StackPanel>

                    <DataGrid ItemsSource="{Binding RecentSales}"
                              Style="{StaticResource DataGridStyle}"
                              MaxHeight="400">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ" 
                                                Binding="{Binding SaleDate, StringFormat=yyyy/MM/dd HH:mm}"
                                                Width="140"/>
                            <DataGridTextColumn Header="العميل" 
                                                Binding="{Binding Customer.FullName}"
                                                Width="*"/>
                            <DataGridTextColumn Header="المبلغ" 
                                                Binding="{Binding TotalAmount, StringFormat='{}{0:N0} ل.س'}"
                                                Width="120"/>
                            <DataGridTextColumn Header="الموظف" 
                                                Binding="{Binding User.FullName}"
                                                Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Orientation="Horizontal" 
                                HorizontalAlignment="Center"
                                Margin="0,16,0,0">
                        <Button Style="{StaticResource SecondaryButtonStyle}"
                                Content="عرض تقرير مفصل"
                                Margin="0,0,8,0"/>
                        <Button Style="{StaticResource PrimaryButtonStyle}"
                                Content="تحديث البيانات"
                                Command="{Binding RefreshCommand}"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </ScrollViewer>
</Page>