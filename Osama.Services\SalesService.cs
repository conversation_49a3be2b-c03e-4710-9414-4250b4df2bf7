using Osama.Data.Repositories;
using Osama.Models;
using System.Linq;

namespace Osama.Services
{
    /// <summary>
    /// خدمة المبيعات
    /// </summary>
    public class SalesService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AuthenticationService _authService;
        private readonly InventoryService _inventoryService;

        public SalesService(IUnitOfWork unitOfWork, AuthenticationService authService, InventoryService inventoryService)
        {
            _unitOfWork = unitOfWork;
            _authService = authService;
            _inventoryService = inventoryService;
        }

        /// <summary>
        /// إنشاء اشتراك جديد
        /// </summary>
        /// <param name="customerName">اسم المشترك</param>
        /// <param name="subscriptionFee">رسم الاشتراك</param>
        /// <param name="routerProductId">معرف منتج الراوتر</param>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <param name="address">العنوان</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>المبيعة الجديدة</returns>
        public async Task<Sale?> CreateNewSubscriptionAsync(string customerName, decimal subscriptionFee, int? routerProductId = null, string? phoneNumber = null, string? address = null, string? notes = null)
        {
            if (_authService.CurrentUser == null || _authService.CurrentShift == null)
                return null;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // إنشاء العميل أو البحث عنه
                var customer = await _unitOfWork.Customers.FirstOrDefaultAsync(c => c.FullName == customerName);
                if (customer == null)
                {
                    customer = new Customer
                    {
                        FullName = customerName,
                        PhoneNumber = phoneNumber,
                        Address = address,
                        RegistrationDate = DateTime.Now,
                        IsActive = true
                    };
                    await _unitOfWork.Customers.AddAsync(customer);
                }

                // حساب السعر الإجمالي
                decimal routerPrice = 0;
                if (routerProductId.HasValue)
                {
                    var router = await _unitOfWork.Products.GetByIdAsync(routerProductId.Value);
                    if (router != null)
                    {
                        routerPrice = router.SalePrice;
                    }
                }

                decimal totalAmount = subscriptionFee + routerPrice;

                // إنشاء المبيعة
                var sale = new Sale
                {
                    CustomerId = customer.CustomerId,
                    UserId = _authService.CurrentUser.UserId,
                    RouterProductId = routerProductId,
                    SubscriptionFee = subscriptionFee,
                    RouterPrice = routerPrice,
                    TotalAmount = totalAmount,
                    SaleDate = DateTime.Now,
                    ShiftId = _authService.CurrentShift.ShiftId,
                    Notes = notes,
                    IsPrinted = false
                };

                await _unitOfWork.Sales.AddAsync(sale);

                // تحديث رصيد الشفت
                _authService.CurrentShift.TotalSales += totalAmount;
                _authService.CurrentShift.ClosingBalance += totalAmount;
                await _unitOfWork.Shifts.UpdateAsync(_authService.CurrentShift);

                await _unitOfWork.CommitTransactionAsync();
                return sale;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return null;
            }
        }

        /// <summary>
        /// تسليم راوتر للعميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <param name="routerProductId">معرف منتج الراوتر</param>
        /// <param name="cableProductId">معرف منتج الكابل</param>
        /// <param name="packageId">معرف الباقة</param>
        /// <param name="installerId">معرف عامل التركيب</param>
        /// <param name="subscriptionFee">رسم الاشتراك</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>عملية التسليم</returns>
        public async Task<RouterDelivery?> DeliverRouterAsync(int customerId, int routerProductId, int cableProductId, int packageId, int installerId, decimal subscriptionFee, string? notes = null)
        {
            if (_authService.CurrentUser == null || _authService.CurrentShift == null)
                return null;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // التحقق من توفر المنتجات
                var router = await _unitOfWork.Products.GetByIdAsync(routerProductId);
                var cable = await _unitOfWork.Products.GetByIdAsync(cableProductId);
                var package = await _unitOfWork.Packages.GetByIdAsync(packageId);

                if (router == null || cable == null || package == null)
                    return null;

                // التحقق من المخزون
                if (router.CurrentStock <= 0)
                    throw new InvalidOperationException("الراوتر غير متوفر في المخزون");

                // التحقق من مخزون العامل للكابل
                var workerInventory = await _unitOfWork.WorkerInventories.FirstOrDefaultAsync(wi => wi.UserId == installerId && wi.IsActive);
                if (workerInventory == null)
                    throw new InvalidOperationException("لا يوجد مخزون للعامل");

                var workerCableItem = await _unitOfWork.WorkerInventoryItems.FirstOrDefaultAsync(wii => wii.WorkerInventoryId == workerInventory.WorkerInventoryId && wii.ProductId == cableProductId);
                if (workerCableItem == null || workerCableItem.Quantity <= 0)
                    throw new InvalidOperationException("الكابل غير متوفر في مخزون العامل");

                // حساب المبلغ الإجمالي
                decimal totalAmount = subscriptionFee + package.Price;

                // إنشاء عملية التسليم
                var delivery = new RouterDelivery
                {
                    CustomerId = customerId,
                    UserId = _authService.CurrentUser.UserId,
                    RouterProductId = routerProductId,
                    CableProductId = cableProductId,
                    PackageId = packageId,
                    InstallerId = installerId,
                    SubscriptionFee = subscriptionFee,
                    PackagePrice = package.Price,
                    TotalAmount = totalAmount,
                    DeliveryDate = DateTime.Now,
                    ShiftId = _authService.CurrentShift.ShiftId,
                    Notes = notes,
                    IsPrinted = false
                };

                await _unitOfWork.RouterDeliveries.AddAsync(delivery);

                // خصم الراوتر من المخزون العام
                await _inventoryService.AddInventoryTransactionAsync(routerProductId, _authService.CurrentUser.UserId, InventoryTransactionType.سحب, 1, router.SalePrice, "تسليم راوتر للعميل", delivery.DeliveryId);

                // خصم الكابل من مخزون العامل
                workerCableItem.Quantity -= 1;
                workerCableItem.LastUpdated = DateTime.Now;
                await _unitOfWork.WorkerInventoryItems.UpdateAsync(workerCableItem);

                // تحديث باقة العميل
                var customer = await _unitOfWork.Customers.GetByIdAsync(customerId);
                if (customer != null)
                {
                    customer.CurrentPackageId = packageId;
                    customer.PackageStartDate = DateTime.Now;
                    customer.PackageEndDate = DateTime.Now.AddDays(package.DurationDays);
                    await _unitOfWork.Customers.UpdateAsync(customer);
                }

                // تحديث رصيد الشفت
                _authService.CurrentShift.TotalSales += totalAmount;
                _authService.CurrentShift.ClosingBalance += totalAmount;
                await _unitOfWork.Shifts.UpdateAsync(_authService.CurrentShift);

                await _unitOfWork.CommitTransactionAsync();
                return delivery;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return null;
            }
        }

        /// <summary>
        /// تجديد باقة العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <param name="packageId">معرف الباقة</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>عملية التجديد</returns>
        public async Task<PackageRenewal?> RenewPackageAsync(int customerId, int packageId, string? notes = null)
        {
            if (_authService.CurrentUser == null || _authService.CurrentShift == null)
                return null;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var customer = await _unitOfWork.Customers.GetByIdAsync(customerId);
                var package = await _unitOfWork.Packages.GetByIdAsync(packageId);

                if (customer == null || package == null)
                    return null;

                // تحديد تاريخ البداية والنهاية
                DateTime startDate = customer.PackageEndDate?.Date > DateTime.Now.Date ? customer.PackageEndDate.Value : DateTime.Now;
                DateTime endDate = startDate.AddDays(package.DurationDays);

                // إنشاء عملية التجديد
                var renewal = new PackageRenewal
                {
                    CustomerId = customerId,
                    PackageId = packageId,
                    UserId = _authService.CurrentUser.UserId,
                    Amount = package.Price,
                    RenewalDate = DateTime.Now,
                    StartDate = startDate,
                    EndDate = endDate,
                    ShiftId = _authService.CurrentShift.ShiftId,
                    Notes = notes
                };

                await _unitOfWork.PackageRenewals.AddAsync(renewal);

                // تحديث بيانات العميل
                customer.CurrentPackageId = packageId;
                customer.PackageStartDate = startDate;
                customer.PackageEndDate = endDate;
                await _unitOfWork.Customers.UpdateAsync(customer);

                // تحديث رصيد الشفت
                _authService.CurrentShift.TotalSales += package.Price;
                _authService.CurrentShift.ClosingBalance += package.Price;
                await _unitOfWork.Shifts.UpdateAsync(_authService.CurrentShift);

                await _unitOfWork.CommitTransactionAsync();
                return renewal;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return null;
            }
        }

        /// <summary>
        /// الحصول على المبيعات حسب الفترة
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة المبيعات</returns>
        public async Task<IEnumerable<Sale>> GetSalesByPeriodAsync(DateTime startDate, DateTime endDate)
        {
            var result = await _unitOfWork.Sales.FindWithIncludeAsync(
                s => s.SaleDate >= startDate && s.SaleDate <= endDate,
                s => s.Customer!,
                s => s.User!,
                s => s.RouterProduct!
            );
            return result ?? Enumerable.Empty<Sale>();
        }

        /// <summary>
        /// الحصول على إجمالي المبيعات لفترة معينة
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>إجمالي المبيعات</returns>
        public async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            var sales = await _unitOfWork.Sales.FindAsync(s => s.SaleDate >= startDate && s.SaleDate <= endDate);
            var deliveries = await _unitOfWork.RouterDeliveries.FindAsync(rd => rd.DeliveryDate >= startDate && rd.DeliveryDate <= endDate);
            var renewals = await _unitOfWork.PackageRenewals.FindAsync(pr => pr.RenewalDate >= startDate && pr.RenewalDate <= endDate);

            return sales.Sum(s => s.TotalAmount) + deliveries.Sum(d => d.TotalAmount) + renewals.Sum(r => r.Amount);
        }

        /// <summary>
        /// الحصول على العملاء المستحقين للتجديد
        /// </summary>
        /// <param name="daysBeforeExpiry">عدد الأيام قبل انتهاء الباقة</param>
        /// <returns>قائمة العملاء</returns>
        public async Task<IEnumerable<Customer>> GetCustomersDueForRenewalAsync(int daysBeforeExpiry = 7)
        {
            var targetDate = DateTime.Now.AddDays(daysBeforeExpiry);
            return await _unitOfWork.Customers.FindWithIncludeAsync(
                c => c.PackageEndDate.HasValue && c.PackageEndDate.Value <= targetDate && c.IsActive,
                c => c.CurrentPackage!
            );
        }
    }
}