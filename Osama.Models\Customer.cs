using System.ComponentModel.DataAnnotations;

namespace Osama.Models
{
    /// <summary>
    /// نموذج العميل/المشترك
    /// </summary>
    public class Customer
    {
        [Key]
        public int CustomerId { get; set; }

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(15)]
        public string? PhoneNumber { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(50)]
        public string? NationalId { get; set; }

        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        public string? Notes { get; set; }

        // معلومات الاشتراك الحالي
        public int? CurrentPackageId { get; set; }
        public DateTime? PackageStartDate { get; set; }
        public DateTime? PackageEndDate { get; set; }

        // العلاقات
        public virtual Package? CurrentPackage { get; set; }
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<RouterDelivery> RouterDeliveries { get; set; } = new List<RouterDelivery>();
        public virtual ICollection<PackageRenewal> PackageRenewals { get; set; } = new List<PackageRenewal>();
    }
}