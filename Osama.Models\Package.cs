using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج الباقة - باقات الإنترنت المختلفة
    /// </summary>
    public class Package
    {
        [Key]
        public int PackageId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        public int SpeedMbps { get; set; }

        public int DurationDays { get; set; } = 30; // مدة الباقة بالأيام

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<RouterDelivery> RouterDeliveries { get; set; } = new List<RouterDelivery>();
        public virtual ICollection<PackageRenewal> PackageRenewals { get; set; } = new List<PackageRenewal>();
    }
}