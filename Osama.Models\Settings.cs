using System.ComponentModel.DataAnnotations;

namespace Osama.Models
{
    /// <summary>
    /// نموذج الإعدادات
    /// </summary>
    public class Settings
    {
        [Key]
        public int SettingId { get; set; }

        [Required]
        [StringLength(100)]
        public string Key { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Value { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Description { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// مفاتيح الإعدادات الثابتة
    /// </summary>
    public static class SettingsKeys
    {
        public const string SubscriptionFee = "SubscriptionFee";
        public const string TaxRate = "TaxRate";
        public const string Currency = "Currency";
        public const string CompanyName = "CompanyName";
        public const string CompanyAddress = "CompanyAddress";
        public const string CompanyPhone = "CompanyPhone";
        public const string CompanyEmail = "CompanyEmail";
        public const string DollarExchangeRate = "DollarExchangeRate";
        public const string MinimumStockAlert = "MinimumStockAlert";
        public const string BackupPath = "BackupPath";
        public const string PrinterName = "PrinterName";
    }
}