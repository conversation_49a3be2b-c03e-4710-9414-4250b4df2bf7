<Window x:Class="Osama.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام أسامة - إدارة شركات الإنترنت والمحاسبة"
        Style="{StaticResource MainWindowStyle}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العلوي -->
            <materialDesign:ColorZone Grid.Row="0"
                                      Mode="PrimaryDark"
                                      Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- معلومات المستخدم والشفت -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Account"
                                                 Width="24" Height="24"
                                                 Foreground="White"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,8,0"/>
                        <StackPanel>
                            <TextBlock Text="{Binding CurrentUserName}"
                                       Foreground="White"
                                       FontFamily="{StaticResource ArabicFontBold}"
                                       FontSize="14"/>
                            <TextBlock Text="{Binding CurrentUserRole}"
                                       Foreground="White"
                                       FontSize="12"
                                       Opacity="0.8"/>
                        </StackPanel>

                        <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"
                                   Margin="16,0"/>

                        <materialDesign:PackIcon Kind="ClockOutline"
                                                 Width="20" Height="20"
                                                 Foreground="White"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,8,0"/>
                        <StackPanel>
                            <TextBlock Text="{Binding ShiftStartTime}"
                                       Foreground="White"
                                       FontSize="12"/>
                            <TextBlock Text="{Binding CurrentShiftBalance, StringFormat='الرصيد: {0:N0} ل.س'}"
                                       Foreground="White"
                                       FontSize="12"
                                       Opacity="0.8"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- عنوان التطبيق -->
                    <TextBlock Grid.Column="1"
                               Text="نظام أسامة"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"
                               FontFamily="{StaticResource ArabicFontBold}"
                               FontSize="18"/>

                    <!-- أزرار التحكم -->
                    <StackPanel Grid.Column="2" 
                                Orientation="Horizontal"
                                FlowDirection="LeftToRight">
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                                ToolTip="الإعدادات"
                                Foreground="White"
                                Margin="4,0">
                            <materialDesign:PackIcon Kind="Settings" Width="20" Height="20"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                                ToolTip="إغلاق الشفت"
                                Foreground="White"
                                Margin="4,0"
                                Click="CloseShiftButton_Click">
                            <materialDesign:PackIcon Kind="ExitToApp" Width="20" Height="20"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                                ToolTip="تصغير"
                                Foreground="White"
                                Margin="4,0"
                                Click="MinimizeButton_Click">
                            <materialDesign:PackIcon Kind="WindowMinimize" Width="20" Height="20"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                                ToolTip="إغلاق"
                                Foreground="White"
                                Margin="4,0"
                                Click="CloseButton_Click">
                            <materialDesign:PackIcon Kind="WindowClose" Width="20" Height="20"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- المحتوى الرئيسي -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="280"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- القائمة الجانبية -->
                <materialDesign:ColorZone Grid.Column="0"
                                          Mode="Light"
                                          materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,16">
                            <!-- لوحة التحكم -->
                            <Expander Header="لوحة التحكم"
                                      IsExpanded="True"
                                      Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="الرئيسية"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="Dashboard">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="ViewDashboard" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>
                                </StackPanel>
                            </Expander>

                            <!-- المبيعات -->
                            <Expander Header="المبيعات"
                                      Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="اشتراك جديد"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="NewSubscription">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="AccountPlus" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="تسليم راوتر"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="RouterDelivery">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="RouterWireless" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="تجديد باقة"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="PackageRenewal">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Refresh" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>
                                </StackPanel>
                            </Expander>

                            <!-- المالية -->
                            <Expander Header="المالية"
                                      Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="المصاريف"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="Expenses">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="CashMinus" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="إغلاق الصناديق"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="CloseCashbox">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="CashRegister" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="شراء الدولار"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="BuyDollar">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>
                                </StackPanel>
                            </Expander>

                            <!-- المخزون -->
                            <Expander Header="المخزون"
                                      Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="إدارة الجرد"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="Inventory">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Package" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="تسليم للعمال"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="WorkerDelivery">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="TruckDelivery" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>
                                </StackPanel>
                            </Expander>

                            <!-- التقارير -->
                            <Expander Header="التقارير"
                                      Style="{StaticResource MaterialDesignExpander}">
                                <StackPanel>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="تقرير المبيعات"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="SalesReport">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="ChartLine" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Content="الأرباح والخسائر"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Right"
                                            Padding="16,8"
                                            Click="NavigateButton_Click"
                                            Tag="ProfitLossReport">
                                        <Button.ContentTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="TrendingUp" 
                                                                             Width="20" Height="20"
                                                                             Margin="0,0,12,0"/>
                                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </Button.ContentTemplate>
                                    </Button>
                                </StackPanel>
                            </Expander>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:ColorZone>

                <!-- منطقة المحتوى -->
                <Grid Grid.Column="1">
                    <Frame x:Name="MainFrame"
                           NavigationUIVisibility="Hidden"
                           Background="White"/>
                </Grid>
            </Grid>

            <!-- شريط الحالة -->
            <materialDesign:ColorZone Grid.Row="2"
                                      Mode="Light"
                                      Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="{Binding StatusMessage, FallbackValue='جاهز'}"
                               VerticalAlignment="Center"
                               FontSize="12"/>

                    <StackPanel Grid.Column="1" 
                                Orientation="Horizontal">
                        <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy/MM/dd HH:mm:ss'}"
                                   VerticalAlignment="Center"
                                   FontSize="12"
                                   Margin="0,0,16,0"
                                   xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                        
                        <materialDesign:PackIcon Kind="Circle"
                                                 Width="12" Height="12"
                                                 Foreground="{StaticResource SuccessBrush}"
                                                 VerticalAlignment="Center"/>
                        <TextBlock Text="متصل"
                                   VerticalAlignment="Center"
                                   FontSize="12"
                                   Margin="4,0,0,0"/>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>