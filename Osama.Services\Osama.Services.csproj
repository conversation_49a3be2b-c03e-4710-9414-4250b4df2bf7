<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="iText7" Version="8.0.2" />
    <PackageReference Include="ClosedXML" Version="0.102.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Osama.Models\Osama.Models.csproj" />
    <ProjectReference Include="..\Osama.Data\Osama.Data.csproj" />
  </ItemGroup>

</Project>