<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- قالب نافذة مخصصة -->
    <ControlTemplate x:Key="CustomWindowTemplate" TargetType="{x:Type Window}">
        <Border Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط العنوان -->
                <Border Grid.Row="0" 
                        Background="{StaticResource PrimaryBrush}"
                        Height="40"
                        MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- عنوان النافذة -->
                        <TextBlock Grid.Column="0"
                                   Text="{TemplateBinding Title}"
                                   Foreground="White"
                                   FontFamily="{StaticResource ArabicFontBold}"
                                   FontSize="16"
                                   VerticalAlignment="Center"
                                   Margin="16,0"/>

                        <!-- أزرار التحكم -->
                        <StackPanel Grid.Column="1" 
                                    Orientation="Horizontal"
                                    FlowDirection="LeftToRight">
                            <Button Name="MinimizeButton"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Width="40" Height="40"
                                    Foreground="White"
                                    Click="MinimizeButton_Click">
                                <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                            </Button>
                            <Button Name="MaximizeButton"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Width="40" Height="40"
                                    Foreground="White"
                                    Click="MaximizeButton_Click">
                                <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16"/>
                            </Button>
                            <Button Name="CloseButton"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Width="40" Height="40"
                                    Foreground="White"
                                    Click="CloseButton_Click">
                                <materialDesign:PackIcon Kind="WindowClose" Width="16" Height="16"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- محتوى النافذة -->
                <ContentPresenter Grid.Row="1"/>
            </Grid>
        </Border>
    </ControlTemplate>

    <!-- قالب بطاقة إحصائية -->
    <DataTemplate x:Key="StatCardTemplate">
        <materialDesign:Card Style="{StaticResource StatCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الأيقونة والعنوان -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,8">
                    <materialDesign:PackIcon Kind="{Binding Icon}" 
                                             Style="{StaticResource StatusIconStyle}"
                                             Foreground="{Binding IconColor}"/>
                    <TextBlock Text="{Binding Title}" 
                               Style="{StaticResource SubHeaderStyle}"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <!-- القيمة -->
                <TextBlock Grid.Row="1"
                           Text="{Binding Value}"
                           FontFamily="{StaticResource ArabicFontBold}"
                           FontSize="{StaticResource ExtraLargeFontSize}"
                           Foreground="{Binding ValueColor}"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>

                <!-- الوصف -->
                <TextBlock Grid.Row="2"
                           Text="{Binding Description}"
                           Style="{StaticResource BodyTextStyle}"
                           HorizontalAlignment="Center"
                           FontSize="{StaticResource SmallFontSize}"/>
            </Grid>
        </materialDesign:Card>
    </DataTemplate>

    <!-- قالب عنصر قائمة التنقل -->
    <DataTemplate x:Key="NavigationItemTemplate">
        <Border Background="Transparent"
                Padding="16,12"
                Margin="4,2">
            <Border.Style>
                <Style TargetType="Border">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        </DataTrigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E3F2FD"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>

            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="{Binding Icon}"
                                         Width="20" Height="20"
                                         Margin="0,0,12,0"
                                         VerticalAlignment="Center">
                    <materialDesign:PackIcon.Style>
                        <Style TargetType="materialDesign:PackIcon">
                            <Setter Property="Foreground" Value="#666666"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                    <Setter Property="Foreground" Value="White"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </materialDesign:PackIcon.Style>
                </materialDesign:PackIcon>

                <TextBlock Text="{Binding Title}"
                           FontFamily="{StaticResource ArabicFont}"
                           FontSize="{StaticResource NormalFontSize}"
                           VerticalAlignment="Center">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Setter Property="Foreground" Value="#333333"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontFamily" Value="{StaticResource ArabicFontBold}"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StackPanel>
        </Border>
    </DataTemplate>

    <!-- قالب رسالة التحميل -->
    <DataTemplate x:Key="LoadingTemplate">
        <StackPanel HorizontalAlignment="Center" 
                    VerticalAlignment="Center"
                    Margin="32">
            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                         Width="48" Height="48"
                         IsIndeterminate="True"
                         Margin="0,0,0,16"/>
            <TextBlock Text="{Binding BusyMessage}"
                       Style="{StaticResource BodyTextStyle}"
                       HorizontalAlignment="Center"/>
        </StackPanel>
    </DataTemplate>

    <!-- قالب رسالة خطأ -->
    <DataTemplate x:Key="ErrorTemplate">
        <StackPanel HorizontalAlignment="Center" 
                    VerticalAlignment="Center"
                    Margin="32">
            <materialDesign:PackIcon Kind="AlertCircle"
                                     Width="48" Height="48"
                                     Foreground="{StaticResource ErrorBrush}"
                                     Margin="0,0,0,16"/>
            <TextBlock Text="{Binding ErrorMessage}"
                       Style="{StaticResource ErrorTextStyle}"
                       HorizontalAlignment="Center"
                       TextAlignment="Center"/>
        </StackPanel>
    </DataTemplate>

    <!-- قالب بيانات فارغة -->
    <DataTemplate x:Key="EmptyDataTemplate">
        <StackPanel HorizontalAlignment="Center" 
                    VerticalAlignment="Center"
                    Margin="32">
            <materialDesign:PackIcon Kind="DatabaseOff"
                                     Width="48" Height="48"
                                     Foreground="#CCCCCC"
                                     Margin="0,0,0,16"/>
            <TextBlock Text="لا توجد بيانات للعرض"
                       Style="{StaticResource BodyTextStyle}"
                       HorizontalAlignment="Center"
                       Foreground="#999999"/>
        </StackPanel>
    </DataTemplate>

</ResourceDictionary>