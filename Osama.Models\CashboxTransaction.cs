using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج معاملات الصندوق
    /// </summary>
    public class CashboxTransaction
    {
        [Key]
        public int TransactionId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public TransactionType Type { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        public int ShiftId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual Shift Shift { get; set; } = null!;
    }

    /// <summary>
    /// أنواع معاملات الصندوق
    /// </summary>
    public enum TransactionType
    {
        إيداع = 1,          // إيداع في الصندوق
        سحب = 2,            // سحب من الصندوق
        تحويل_للخزينة = 3,   // تحويل للخزينة اليومية
        شراء_دولار = 4,     // شراء دولار
        بيع_دولار = 5       // بيع دولار
    }
}