using Osama.Data.Repositories;
using Osama.Models;

namespace Osama.Services
{
    /// <summary>
    /// خدمة العمليات المالية
    /// </summary>
    public class FinancialService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AuthenticationService _authService;

        public FinancialService(IUnitOfWork unitOfWork, AuthenticationService authService)
        {
            _unitOfWork = unitOfWork;
            _authService = authService;
        }

        /// <summary>
        /// إضافة مصروف
        /// </summary>
        /// <param name="description">الوصف</param>
        /// <param name="category">التصنيف</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="receiptNumber">رقم الإيصال</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>المصروف</returns>
        public async Task<Expense?> AddExpenseAsync(string description, ExpenseCategory category, decimal amount, string? receiptNumber = null, string? notes = null)
        {
            if (_authService.CurrentUser == null || _authService.CurrentShift == null)
                return null;

            try
            {
                var expense = new Expense
                {
                    Description = description,
                    Category = category,
                    Amount = amount,
                    UserId = _authService.CurrentUser.UserId,
                    ExpenseDate = DateTime.Now,
                    ShiftId = _authService.CurrentShift.ShiftId,
                    ReceiptNumber = receiptNumber,
                    Notes = notes
                };

                await _unitOfWork.Expenses.AddAsync(expense);

                // تحديث رصيد الشفت
                _authService.CurrentShift.TotalExpenses += amount;
                _authService.CurrentShift.ClosingBalance -= amount;
                await _unitOfWork.Shifts.UpdateAsync(_authService.CurrentShift);

                return expense;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// إغلاق الصندوق وتحويل المبلغ للخزينة اليومية
        /// </summary>
        /// <param name="amount">المبلغ المحول</param>
        /// <param name="description">الوصف</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<bool> CloseCashboxAsync(decimal amount, string description, string? notes = null)
        {
            if (_authService.CurrentUser == null || _authService.CurrentShift == null)
                return false;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // إضافة معاملة صندوق
                var cashboxTransaction = new CashboxTransaction
                {
                    UserId = _authService.CurrentUser.UserId,
                    Type = TransactionType.تحويل_للخزينة,
                    Amount = amount,
                    Description = description,
                    TransactionDate = DateTime.Now,
                    ShiftId = _authService.CurrentShift.ShiftId,
                    Notes = notes
                };

                await _unitOfWork.CashboxTransactions.AddAsync(cashboxTransaction);

                // تحديث الخزينة اليومية
                var dailyTreasury = await _unitOfWork.Treasuries.FirstOrDefaultAsync(t => t.Type == TreasuryType.خزينة_يومية && t.IsActive);
                if (dailyTreasury != null)
                {
                    dailyTreasury.SyrianPoundBalance += amount;
                    dailyTreasury.LastUpdated = DateTime.Now;
                    await _unitOfWork.Treasuries.UpdateAsync(dailyTreasury);

                    // إضافة معاملة خزينة
                    var treasuryTransaction = new TreasuryTransaction
                    {
                        TreasuryId = dailyTreasury.TreasuryId,
                        UserId = _authService.CurrentUser.UserId,
                        Type = TreasuryTransactionType.تحويل_من_صندوق,
                        SyrianPoundAmount = amount,
                        DollarAmount = 0,
                        ExchangeRate = 0,
                        Description = description,
                        TransactionDate = DateTime.Now,
                        Notes = notes
                    };

                    await _unitOfWork.TreasuryTransactions.AddAsync(treasuryTransaction);
                }

                await _unitOfWork.CommitTransactionAsync();
                return true;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return false;
            }
        }

        /// <summary>
        /// شراء دولار من الخزينة اليومية
        /// </summary>
        /// <param name="dollarAmount">مبلغ الدولار</param>
        /// <param name="exchangeRate">سعر الصرف</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<bool> BuyDollarAsync(decimal dollarAmount, decimal exchangeRate, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return false;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var syrianPoundAmount = dollarAmount * exchangeRate;

                // الحصول على الخزينة اليومية
                var dailyTreasury = await _unitOfWork.Treasuries.FirstOrDefaultAsync(t => t.Type == TreasuryType.خزينة_يومية && t.IsActive);
                if (dailyTreasury == null || dailyTreasury.SyrianPoundBalance < syrianPoundAmount)
                    return false;

                // تحديث أرصدة الخزينة
                dailyTreasury.SyrianPoundBalance -= syrianPoundAmount;
                dailyTreasury.DollarBalance += dollarAmount;
                dailyTreasury.LastUpdated = DateTime.Now;
                await _unitOfWork.Treasuries.UpdateAsync(dailyTreasury);

                // إضافة معاملة الخزينة
                var treasuryTransaction = new TreasuryTransaction
                {
                    TreasuryId = dailyTreasury.TreasuryId,
                    UserId = _authService.CurrentUser.UserId,
                    Type = TreasuryTransactionType.شراء_دولار,
                    SyrianPoundAmount = -syrianPoundAmount,
                    DollarAmount = dollarAmount,
                    ExchangeRate = exchangeRate,
                    Description = $"شراء {dollarAmount} دولار بسعر {exchangeRate}",
                    TransactionDate = DateTime.Now,
                    Notes = notes
                };

                await _unitOfWork.TreasuryTransactions.AddAsync(treasuryTransaction);

                await _unitOfWork.CommitTransactionAsync();
                return true;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return false;
            }
        }

        /// <summary>
        /// نقل الأموال من الخزينة اليومية إلى الخزينة الرئيسية
        /// </summary>
        /// <param name="syrianPoundAmount">مبلغ الليرة السورية</param>
        /// <param name="dollarAmount">مبلغ الدولار</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<bool> TransferToMainTreasuryAsync(decimal syrianPoundAmount, decimal dollarAmount, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return false;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                // الحصول على الخزائن
                var dailyTreasury = await _unitOfWork.Treasuries.FirstOrDefaultAsync(t => t.Type == TreasuryType.خزينة_يومية && t.IsActive);
                var mainTreasury = await _unitOfWork.Treasuries.FirstOrDefaultAsync(t => t.Type == TreasuryType.خزينة_رئيسية && t.IsActive);

                if (dailyTreasury == null || mainTreasury == null)
                    return false;

                // التحقق من توفر الأرصدة
                if (dailyTreasury.SyrianPoundBalance < syrianPoundAmount || dailyTreasury.DollarBalance < dollarAmount)
                    return false;

                // تحديث الخزينة اليومية
                dailyTreasury.SyrianPoundBalance -= syrianPoundAmount;
                dailyTreasury.DollarBalance -= dollarAmount;
                dailyTreasury.LastUpdated = DateTime.Now;
                await _unitOfWork.Treasuries.UpdateAsync(dailyTreasury);

                // تحديث الخزينة الرئيسية
                mainTreasury.SyrianPoundBalance += syrianPoundAmount;
                mainTreasury.DollarBalance += dollarAmount;
                mainTreasury.LastUpdated = DateTime.Now;
                await _unitOfWork.Treasuries.UpdateAsync(mainTreasury);

                // إضافة معاملات الخزينة
                var dailyTransaction = new TreasuryTransaction
                {
                    TreasuryId = dailyTreasury.TreasuryId,
                    UserId = _authService.CurrentUser.UserId,
                    Type = TreasuryTransactionType.تحويل_للخزينة_الرئيسية,
                    SyrianPoundAmount = -syrianPoundAmount,
                    DollarAmount = -dollarAmount,
                    ExchangeRate = 0,
                    Description = "تحويل للخزينة الرئيسية",
                    TransactionDate = DateTime.Now,
                    Notes = notes
                };

                var mainTransaction = new TreasuryTransaction
                {
                    TreasuryId = mainTreasury.TreasuryId,
                    UserId = _authService.CurrentUser.UserId,
                    Type = TreasuryTransactionType.إيداع_ليرة,
                    SyrianPoundAmount = syrianPoundAmount,
                    DollarAmount = dollarAmount,
                    ExchangeRate = 0,
                    Description = "تحويل من الخزينة اليومية",
                    TransactionDate = DateTime.Now,
                    Notes = notes
                };

                await _unitOfWork.TreasuryTransactions.AddAsync(dailyTransaction);
                await _unitOfWork.TreasuryTransactions.AddAsync(mainTransaction);

                await _unitOfWork.CommitTransactionAsync();
                return true;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return false;
            }
        }

        /// <summary>
        /// شحن رصيد للموزع
        /// </summary>
        /// <param name="distributorId">معرف الموزع</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="description">الوصف</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>عملية الشحن</returns>
        public async Task<BalanceRecharge?> RechargeDistributorBalanceAsync(int distributorId, decimal amount, string description, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return null;

            try
            {
                await _unitOfWork.BeginTransactionAsync();

                var distributor = await _unitOfWork.Distributors.GetByIdAsync(distributorId);
                if (distributor == null)
                    return null;

                // إنشاء عملية الشحن
                var recharge = new BalanceRecharge
                {
                    DistributorId = distributorId,
                    UserId = _authService.CurrentUser.UserId,
                    Amount = amount,
                    Description = description,
                    RechargeDate = DateTime.Now,
                    Notes = notes
                };

                await _unitOfWork.BalanceRecharges.AddAsync(recharge);

                // تحديث رصيد الموزع
                distributor.CurrentBalance += amount;
                await _unitOfWork.Distributors.UpdateAsync(distributor);

                await _unitOfWork.CommitTransactionAsync();
                return recharge;
            }
            catch (Exception)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return null;
            }
        }

        /// <summary>
        /// إنشاء سند قبض
        /// </summary>
        /// <param name="voucherNumber">رقم السند</param>
        /// <param name="distributorId">معرف الموزع</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="description">الوصف</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>سند القبض</returns>
        public async Task<ReceiptVoucher?> CreateReceiptVoucherAsync(string voucherNumber, int distributorId, decimal amount, string description, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return null;

            try
            {
                var voucher = new ReceiptVoucher
                {
                    VoucherNumber = voucherNumber,
                    DistributorId = distributorId,
                    UserId = _authService.CurrentUser.UserId,
                    Amount = amount,
                    Description = description,
                    VoucherDate = DateTime.Now,
                    Notes = notes
                };

                return await _unitOfWork.ReceiptVouchers.AddAsync(voucher);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// إنشاء سند دفع
        /// </summary>
        /// <param name="voucherNumber">رقم السند</param>
        /// <param name="supplierId">معرف المورد</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="description">الوصف</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>سند الدفع</returns>
        public async Task<PaymentVoucher?> CreatePaymentVoucherAsync(string voucherNumber, int supplierId, decimal amount, string description, string? notes = null)
        {
            if (_authService.CurrentUser == null)
                return null;

            try
            {
                var voucher = new PaymentVoucher
                {
                    VoucherNumber = voucherNumber,
                    SupplierId = supplierId,
                    UserId = _authService.CurrentUser.UserId,
                    Amount = amount,
                    Description = description,
                    VoucherDate = DateTime.Now,
                    Notes = notes
                };

                return await _unitOfWork.PaymentVouchers.AddAsync(voucher);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// تقرير الأرباح والخسائر
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>تقرير الأرباح والخسائر</returns>
        public async Task<ProfitLossReport> GetProfitLossReportAsync(DateTime startDate, DateTime endDate)
        {
            // إجمالي المبيعات
            var sales = await _unitOfWork.Sales.FindAsync(s => s.SaleDate >= startDate && s.SaleDate <= endDate);
            var deliveries = await _unitOfWork.RouterDeliveries.FindAsync(rd => rd.DeliveryDate >= startDate && rd.DeliveryDate <= endDate);
            var renewals = await _unitOfWork.PackageRenewals.FindAsync(pr => pr.RenewalDate >= startDate && pr.RenewalDate <= endDate);

            decimal totalRevenue = sales.Sum(s => s.TotalAmount) + deliveries.Sum(d => d.TotalAmount) + renewals.Sum(r => r.Amount);

            // إجمالي المصاريف
            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.ExpenseDate >= startDate && e.ExpenseDate <= endDate);
            decimal totalExpenses = expenses.Sum(e => e.Amount);

            // تكلفة المنتجات المباعة
            var inventoryTransactions = await _unitOfWork.InventoryTransactions.FindWithIncludeAsync(
                it => it.TransactionDate >= startDate && it.TransactionDate <= endDate && 
                      (it.Type == InventoryTransactionType.سحب || it.Type == InventoryTransactionType.تسليم_عامل || it.Type == InventoryTransactionType.صيانة),
                it => it.Product
            );

            decimal costOfGoodsSold = inventoryTransactions.Sum(it => it.Quantity * it.UnitPrice);

            return new ProfitLossReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRevenue = totalRevenue,
                TotalExpenses = totalExpenses,
                CostOfGoodsSold = costOfGoodsSold,
                GrossProfit = totalRevenue - costOfGoodsSold,
                NetProfit = totalRevenue - costOfGoodsSold - totalExpenses
            };
        }
    }

    /// <summary>
    /// تقرير الأرباح والخسائر
    /// </summary>
    public class ProfitLossReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
    }
}