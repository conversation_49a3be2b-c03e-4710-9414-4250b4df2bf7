using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج المستخدم - يحتوي على بيانات المستخدمين وصلاحياتهم
    /// </summary>
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [Required]
        public UserRole Role { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        [StringLength(15)]
        public string? PhoneNumber { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        // العلاقات
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<Expense> Expenses { get; set; } = new List<Expense>();
        public virtual ICollection<CashboxTransaction> CashboxTransactions { get; set; } = new List<CashboxTransaction>();
        public virtual ICollection<WorkerInventory> WorkerInventories { get; set; } = new List<WorkerInventory>();
    }

    /// <summary>
    /// أدوار المستخدمين في النظام
    /// </summary>
    public enum UserRole
    {
        مدير = 1,           // مدير عام - صلاحيات كاملة
        محاسب = 2,          // محاسب - صلاحيات مالية
        موظف_مبيعات = 3,    // موظف مبيعات
        عامل_تركيب = 4,     // عامل تركيب
        موظف_مخزون = 5      // موظف مخزون
    }
}