using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Osama.Data;
using Osama.Data.Repositories;
using Osama.Services;
using Osama.ViewModels;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace Osama.UI
{
    /// <summary>
    /// تطبيق أسامة الرئيسي
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            base.OnExit(e);
        }

        /// <summary>
        /// الحصول على خدمة من DI Container
        /// </summary>
        /// <typeparam name="T">نوع الخدمة</typeparam>
        /// <returns>الخدمة</returns>
        public static T GetService<T>() where T : class
        {
            return ((App)Current)._host?.Services.GetRequiredService<T>() 
                ?? throw new InvalidOperationException($"Service {typeof(T).Name} not found");
        }

        /// <summary>
        /// الحصول على نص الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>نص الاتصال</returns>
        private string GetConnectionString()
        {
            // استخدام SQLite للتطوير
            return "Data Source=OsamaDB.db";
        }

        /// <summary>
        /// التأكد من إنشاء قاعدة البيانات
        /// </summary>
        private async Task EnsureDatabaseCreatedAsync()
        {
            try
            {
                using var scope = _host?.Services.CreateScope();
                var context = scope?.ServiceProvider.GetRequiredService<OsamaDbContext>();
                
                if (context != null)
                {
                    await context.Database.EnsureCreatedAsync();
                    
                    // التحقق من وجود معامل --seed-data
                    var args = Environment.GetCommandLineArgs();
                    if (args.Contains("--seed-data"))
                    {
                        await DataSeeder.SeedAsync(context);
                        Environment.Exit(0);
                    }
                    
                    // إنشاء البيانات الأساسية إذا كانت قاعدة البيانات فارغة
                    if (!context.Users.Any())
                    {
                        await DataSeeder.SeedAsync(context);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}