<Window x:Class="Osama.UI.Dialogs.CloseShiftDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إغلاق الشفت"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Border Background="White"
            CornerRadius="10"
            materialDesign:ShadowAssist.ShadowDepth="Depth3">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- الرأس -->
            <Border Grid.Row="0"
                    Background="{StaticResource PrimaryBrush}"
                    CornerRadius="10,10,0,0"
                    Padding="20">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ExitToApp"
                                             Width="48" Height="48"
                                             Foreground="White"
                                             Margin="0,0,0,12"/>
                    <TextBlock Text="إغلاق الشفت"
                               Style="{StaticResource HeaderStyle}"
                               Foreground="White"
                               FontSize="20"/>
                </StackPanel>
            </Border>

            <!-- المحتوى -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto"
                          Padding="32">
                <StackPanel>
                    <!-- معلومات الشفت الحالي -->
                    <materialDesign:Card Style="{StaticResource CardStyle}" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="معلومات الشفت الحالي"
                                       Style="{StaticResource SubHeaderStyle}"
                                       Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="تاريخ البداية:" Style="{StaticResource LabelStyle}"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ShiftStartTime}" Style="{StaticResource BodyTextStyle}"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="الرصيد الافتتاحي:" Style="{StaticResource LabelStyle}"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding OpeningBalance, StringFormat='{}{0:N0} ل.س'}" Style="{StaticResource BodyTextStyle}"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي المبيعات:" Style="{StaticResource LabelStyle}"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding TotalSales, StringFormat='{}{0:N0} ل.س'}" Style="{StaticResource BodyTextStyle}" Foreground="{StaticResource SuccessBrush}"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="إجمالي المصاريف:" Style="{StaticResource LabelStyle}"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding TotalExpenses, StringFormat='{}{0:N0} ل.س'}" Style="{StaticResource BodyTextStyle}" Foreground="{StaticResource ErrorBrush}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- الرصيد الختامي -->
                    <StackPanel>
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="الرصيد الختامي الفعلي"
                                 Text="{Binding ClosingBalance, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,16"/>

                        <!-- الفرق -->
                        <Border Background="#FFF3E0"
                                BorderBrush="{StaticResource WarningBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Padding="12"
                                Margin="0,0,0,16"
                                Visibility="{Binding HasDifference, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                         Foreground="{StaticResource WarningBrush}"
                                                         Width="20" Height="20"
                                                         Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding DifferenceMessage}"
                                           Style="{StaticResource BodyTextStyle}"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- ملاحظات -->
                        <TextBox Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="ملاحظات (اختياري)"
                                 Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="80"
                                 VerticalContentAlignment="Top"
                                 Margin="0,0,0,16"/>
                    </StackPanel>

                    <!-- رسالة الخطأ -->
                    <Border Background="#FFEBEE"
                            BorderBrush="{StaticResource ErrorBrush}"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="12"
                            Margin="0,0,0,16"
                            Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                     Foreground="{StaticResource ErrorBrush}"
                                                     Width="20" Height="20"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding ErrorMessage}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- الأزرار -->
            <Border Grid.Row="2"
                    Background="#F5F5F5"
                    CornerRadius="0,0,10,10"
                    Padding="20">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <Button Style="{StaticResource PrimaryButtonStyle}"
                            Content="إغلاق الشفت"
                            Command="{Binding CloseShiftCommand}"
                            IsEnabled="{Binding IsNotBusy}"
                            Margin="0,0,8,0"/>
                    <Button Style="{StaticResource SecondaryButtonStyle}"
                            Content="إلغاء"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>