using Osama.Models;

namespace Osama.Data.Repositories
{
    /// <summary>
    /// واجهة وحدة العمل - تجمع جميع المستودعات
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        // المستودعات الرئيسية
        IRepository<User> Users { get; }
        IRepository<Customer> Customers { get; }
        IRepository<Product> Products { get; }
        IRepository<Package> Packages { get; }
        IRepository<Supplier> Suppliers { get; }
        IRepository<Distributor> Distributors { get; }

        // مستودعات المبيعات
        IRepository<Sale> Sales { get; }
        IRepository<RouterDelivery> RouterDeliveries { get; }
        IRepository<PackageRenewal> PackageRenewals { get; }
        IRepository<Shift> Shifts { get; }

        // مستودعات المشتريات والمصاريف
        IRepository<Purchase> Purchases { get; }
        IRepository<PurchaseItem> PurchaseItems { get; }
        IRepository<Expense> Expenses { get; }

        // مستودعات الخزينة
        IRepository<Treasury> Treasuries { get; }
        IRepository<TreasuryTransaction> TreasuryTransactions { get; }
        IRepository<CashboxTransaction> CashboxTransactions { get; }

        // مستودعات السندات
        IRepository<ReceiptVoucher> ReceiptVouchers { get; }
        IRepository<PaymentVoucher> PaymentVouchers { get; }
        IRepository<BalanceRecharge> BalanceRecharges { get; }

        // مستودعات المخزون
        IRepository<InventoryTransaction> InventoryTransactions { get; }
        IRepository<WorkerInventory> WorkerInventories { get; }
        IRepository<WorkerInventoryItem> WorkerInventoryItems { get; }
        IRepository<MaintenanceOrder> MaintenanceOrders { get; }
        IRepository<MaintenanceOrderItem> MaintenanceOrderItems { get; }

        // مستودع الإعدادات
        IRepository<Settings> Settings { get; }

        // العمليات العامة
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}