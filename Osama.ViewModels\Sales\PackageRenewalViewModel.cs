using Microsoft.Toolkit.Mvvm.Input;
using Osama.Data.Repositories;
using Osama.Models;
using Osama.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace Osama.ViewModels.Sales
{
    /// <summary>
    /// نموذج عرض تجديد باقة
    /// </summary>
    public class PackageRenewalViewModel : BaseViewModel
    {
        private readonly SalesService _salesService;
        private readonly IUnitOfWork _unitOfWork;
        
        // الحقول
        private Customer? _selectedCustomer;
        private Package? _selectedPackage;
        private decimal _packagePrice;
        private int _packageDuration;
        private string _notes = string.Empty;
        private bool _hasSuccess;
        private string _successMessage = string.Empty;
        private PackageRenewal? _lastRenewal;
        private Customer? _selectedCustomerFromList;

        // المجموعات
        private ObservableCollection<Customer> _customers = new();
        private ObservableCollection<Package> _packages = new();
        private ObservableCollection<Customer> _customersDueForRenewal = new();

        public PackageRenewalViewModel(SalesService salesService, IUnitOfWork unitOfWork)
        {
            _salesService = salesService;
            _unitOfWork = unitOfWork;

            // إنشاء الأوامر
            RenewCommand = new AsyncRelayCommand(RenewAsync, () => CanRenew);
            PrintCommand = new AsyncRelayCommand(PrintAsync, () => CanPrint);
            ClearCommand = new RelayCommand(Clear);
            RefreshCommand = new AsyncRelayCommand(RefreshAsync);

            // تحميل البيانات
            _ = LoadDataAsync();
        }

        #region الخصائص

        /// <summary>
        /// العميل المحدد
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار العميل")]
        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                SetProperty(ref _selectedCustomer, value);
                OnPropertyChanged(nameof(HasCurrentPackage));
                OnPropertyChanged(nameof(CurrentPackageName));
                OnPropertyChanged(nameof(PackageEndDate));
                OnPropertyChanged(nameof(PackageStatus));
                OnPropertyChanged(nameof(IsPackageExpired));
                OnPropertyChanged(nameof(RenewalStartDate));
                OnPropertyChanged(nameof(RenewalEndDate));
                RenewCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// العميل المحدد من القائمة
        /// </summary>
        public Customer? SelectedCustomerFromList
        {
            get => _selectedCustomerFromList;
            set
            {
                SetProperty(ref _selectedCustomerFromList, value);
                if (value != null)
                {
                    SelectedCustomer = value;
                }
            }
        }

        /// <summary>
        /// الباقة المحددة
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار نوع الباقة")]
        public Package? SelectedPackage
        {
            get => _selectedPackage;
            set
            {
                SetProperty(ref _selectedPackage, value);
                if (value != null)
                {
                    PackagePrice = value.Price;
                    PackageDuration = value.DurationDays;
                }
                OnPropertyChanged(nameof(HasSelectedPackage));
                OnPropertyChanged(nameof(RenewalStartDate));
                OnPropertyChanged(nameof(RenewalEndDate));
                RenewCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// سعر الباقة
        /// </summary>
        public decimal PackagePrice
        {
            get => _packagePrice;
            set => SetProperty(ref _packagePrice, value);
        }

        /// <summary>
        /// مدة الباقة
        /// </summary>
        public int PackageDuration
        {
            get => _packageDuration;
            set
            {
                SetProperty(ref _packageDuration, value);
                OnPropertyChanged(nameof(RenewalEndDate));
            }
        }

        /// <summary>
        /// الملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// هل يوجد رسالة نجاح
        /// </summary>
        public bool HasSuccess
        {
            get => _hasSuccess;
            set => SetProperty(ref _hasSuccess, value);
        }

        /// <summary>
        /// رسالة النجاح
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// هل يوجد باقة حالية للعميل
        /// </summary>
        public bool HasCurrentPackage => SelectedCustomer?.CurrentPackage != null;

        /// <summary>
        /// هل تم اختيار باقة
        /// </summary>
        public bool HasSelectedPackage => SelectedPackage != null;

        /// <summary>
        /// اسم الباقة الحالية
        /// </summary>
        public string CurrentPackageName => SelectedCustomer?.CurrentPackage?.Name ?? "لا توجد باقة";

        /// <summary>
        /// تاريخ انتهاء الباقة
        /// </summary>
        public DateTime? PackageEndDate => SelectedCustomer?.PackageEndDate;

        /// <summary>
        /// حالة الباقة
        /// </summary>
        public string PackageStatus
        {
            get
            {
                if (SelectedCustomer?.PackageEndDate == null)
                    return "لا توجد باقة";
                
                return IsPackageExpired ? "منتهية الصلاحية" : "سارية";
            }
        }

        /// <summary>
        /// هل الباقة منتهية الصلاحية
        /// </summary>
        public bool IsPackageExpired => SelectedCustomer?.PackageEndDate < DateTime.Now;

        /// <summary>
        /// تاريخ بداية التجديد
        /// </summary>
        public DateTime RenewalStartDate
        {
            get
            {
                if (SelectedCustomer?.PackageEndDate == null)
                    return DateTime.Now;
                
                return SelectedCustomer.PackageEndDate.Value.Date > DateTime.Now.Date 
                    ? SelectedCustomer.PackageEndDate.Value 
                    : DateTime.Now;
            }
        }

        /// <summary>
        /// تاريخ انتهاء التجديد
        /// </summary>
        public DateTime RenewalEndDate => RenewalStartDate.AddDays(PackageDuration);

        /// <summary>
        /// العملاء
        /// </summary>
        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        /// <summary>
        /// الباقات
        /// </summary>
        public ObservableCollection<Package> Packages
        {
            get => _packages;
            set => SetProperty(ref _packages, value);
        }

        /// <summary>
        /// العملاء المستحقون للتجديد
        /// </summary>
        public ObservableCollection<Customer> CustomersDueForRenewal
        {
            get => _customersDueForRenewal;
            set => SetProperty(ref _customersDueForRenewal, value);
        }

        #endregion

        #region الأوامر

        /// <summary>
        /// أمر التجديد
        /// </summary>
        public IAsyncRelayCommand RenewCommand { get; }

        /// <summary>
        /// أمر الطباعة
        /// </summary>
        public IAsyncRelayCommand PrintCommand { get; }

        /// <summary>
        /// أمر المسح
        /// </summary>
        public ICommand ClearCommand { get; }

        /// <summary>
        /// أمر التحديث
        /// </summary>
        public IAsyncRelayCommand RefreshCommand { get; }

        /// <summary>
        /// هل يمكن التجديد
        /// </summary>
        private bool CanRenew => SelectedCustomer != null && SelectedPackage != null && !IsBusy;

        /// <summary>
        /// هل يمكن الطباعة
        /// </summary>
        public bool CanPrint => _lastRenewal != null && !IsBusy;

        #endregion

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // تحميل العملاء
                var customers = await _unitOfWork.Customers.FindWithIncludeAsync(
                    c => c.IsActive,
                    c => c.CurrentPackage!
                );
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                // تحميل الباقات
                var packages = await _unitOfWork.Packages.FindAsync(p => p.IsActive);
                Packages.Clear();
                foreach (var package in packages)
                {
                    Packages.Add(package);
                }

                // تحميل العملاء المستحقين للتجديد
                await LoadCustomersDueForRenewalAsync();

            }, "جاري تحميل البيانات...");
        }

        /// <summary>
        /// تحميل العملاء المستحقين للتجديد
        /// </summary>
        private async Task LoadCustomersDueForRenewalAsync()
        {
            var dueCustomers = await _salesService.GetCustomersDueForRenewalAsync(7);
            CustomersDueForRenewal.Clear();
            foreach (var customer in dueCustomers.Take(20))
            {
                CustomersDueForRenewal.Add(customer);
            }
        }

        /// <summary>
        /// تجديد الباقة
        /// </summary>
        private async Task RenewAsync()
        {
            if (!ValidateData())
                return;

            var result = await ExecuteAsync(async () =>
            {
                var renewal = await _salesService.RenewPackageAsync(
                    SelectedCustomer!.CustomerId,
                    SelectedPackage!.PackageId,
                    Notes
                );

                if (renewal == null)
                {
                    SetError("فشل في تجديد الباقة");
                    return;
                }

                _lastRenewal = renewal;
                SetSuccess("تم تجديد الباقة بنجاح");
                OnPropertyChanged(nameof(CanPrint));

                // تحديث بيانات العميل
                await RefreshCustomerDataAsync();

            }, "جاري تجديد الباقة...");
        }

        /// <summary>
        /// تحديث بيانات العميل
        /// </summary>
        private async Task RefreshCustomerDataAsync()
        {
            if (SelectedCustomer == null) return;

            var updatedCustomers = await _unitOfWork.Customers.FindWithIncludeAsync(
                c => c.CustomerId == SelectedCustomer.CustomerId,
                c => c.CurrentPackage!
            );

            var updatedCustomer = updatedCustomers.FirstOrDefault();
            if (updatedCustomer != null)
            {
                // تحديث العميل في القائمة
                var index = Customers.IndexOf(SelectedCustomer);
                if (index >= 0)
                {
                    Customers[index] = updatedCustomer;
                    SelectedCustomer = updatedCustomer;
                }
            }

            // تحديث قائمة العملاء المستحقين للتجديد
            await LoadCustomersDueForRenewalAsync();
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private async Task PrintAsync()
        {
            // إذا لم يتم التجديد، جدد أولاً
            if (_lastRenewal == null)
            {
                await RenewAsync();
                if (_lastRenewal == null) return;
            }

            await ExecuteAsync(async () =>
            {
                // هنا يتم تنفيذ عملية الطباعة
                await Task.Delay(1000); // محاكاة عملية الطباعة

                SetSuccess("تم طباعة الفاتورة بنجاح");

            }, "جاري طباعة الفاتورة...");
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void Clear()
        {
            SelectedCustomer = null;
            SelectedPackage = null;
            PackagePrice = 0;
            PackageDuration = 0;
            Notes = string.Empty;
            _lastRenewal = null;
            
            ClearError();
            ClearSuccess();
            
            OnPropertyChanged(nameof(CanPrint));
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        public override async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public override bool ValidateData()
        {
            ClearError();
            ClearSuccess();

            if (SelectedCustomer == null)
            {
                SetError("يجب اختيار العميل");
                return false;
            }

            if (SelectedPackage == null)
            {
                SetError("يجب اختيار نوع الباقة");
                return false;
            }

            if (PackagePrice <= 0)
            {
                SetError("سعر الباقة يجب أن يكون أكبر من الصفر");
                return false;
            }

            return true;
        }

        /// <summary>
        /// تعيين رسالة نجاح
        /// </summary>
        private void SetSuccess(string message)
        {
            SuccessMessage = message;
            HasSuccess = true;
        }

        /// <summary>
        /// مسح رسالة النجاح
        /// </summary>
        private void ClearSuccess()
        {
            SuccessMessage = string.Empty;
            HasSuccess = false;
        }
    }
}