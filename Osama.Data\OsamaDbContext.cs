using Microsoft.EntityFrameworkCore;
using Osama.Models;

namespace Osama.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي لتطبيق أسامة
    /// </summary>
    public class OsamaDbContext : DbContext
    {
        public OsamaDbContext(DbContextOptions<OsamaDbContext> options) : base(options)
        {
        }

        // الجداول الرئيسية
        public DbSet<User> Users { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Package> Packages { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Distributor> Distributors { get; set; }

        // المبيعات والعمليات
        public DbSet<Sale> Sales { get; set; }
        public DbSet<RouterDelivery> RouterDeliveries { get; set; }
        public DbSet<PackageRenewal> PackageRenewals { get; set; }
        public DbSet<Shift> Shifts { get; set; }

        // المشتريات والمصاريف
        public DbSet<Purchase> Purchases { get; set; }
        public DbSet<PurchaseItem> PurchaseItems { get; set; }
        public DbSet<Expense> Expenses { get; set; }

        // الخزينة والصناديق
        public DbSet<Treasury> Treasuries { get; set; }
        public DbSet<TreasuryTransaction> TreasuryTransactions { get; set; }
        public DbSet<CashboxTransaction> CashboxTransactions { get; set; }

        // السندات
        public DbSet<ReceiptVoucher> ReceiptVouchers { get; set; }
        public DbSet<PaymentVoucher> PaymentVouchers { get; set; }
        public DbSet<BalanceRecharge> BalanceRecharges { get; set; }

        // المخزون
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<WorkerInventory> WorkerInventories { get; set; }
        public DbSet<WorkerInventoryItem> WorkerInventoryItems { get; set; }
        public DbSet<MaintenanceOrder> MaintenanceOrders { get; set; }
        public DbSet<MaintenanceOrderItem> MaintenanceOrderItems { get; set; }

        // الإعدادات
        public DbSet<Settings> Settings { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والقيود

            // علاقات المستخدم
            modelBuilder.Entity<Sale>()
                .HasOne(s => s.User)
                .WithMany(u => u.Sales)
                .HasForeignKey(s => s.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RouterDelivery>()
                .HasOne(rd => rd.User)
                .WithMany()
                .HasForeignKey(rd => rd.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RouterDelivery>()
                .HasOne(rd => rd.Installer)
                .WithMany()
                .HasForeignKey(rd => rd.InstallerId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات المنتجات
            modelBuilder.Entity<RouterDelivery>()
                .HasOne(rd => rd.RouterProduct)
                .WithMany()
                .HasForeignKey(rd => rd.RouterProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RouterDelivery>()
                .HasOne(rd => rd.CableProduct)
                .WithMany()
                .HasForeignKey(rd => rd.CableProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // فهارس للبحث السريع
            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.FullName);

            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.PhoneNumber);

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Name);

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            // القيم الافتراضية
            modelBuilder.Entity<User>()
                .Property(u => u.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Customer>()
                .Property(c => c.RegistrationDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Product>()
                .Property(p => p.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            // تحويل الـ Enums إلى أرقام
            modelBuilder.Entity<User>()
                .Property(u => u.Role)
                .HasConversion<int>();

            modelBuilder.Entity<Product>()
                .Property(p => p.Category)
                .HasConversion<int>();

            modelBuilder.Entity<Expense>()
                .Property(e => e.Category)
                .HasConversion<int>();

            // بيانات أولية
            SeedData(modelBuilder);
        }

        /// <summary>
        /// إدراج البيانات الأولية
        /// </summary>
        private void SeedData(ModelBuilder modelBuilder)
        {
            // إنشاء المستخدم الافتراضي (المدير)
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    FullName = "مدير النظام",
                    Role = UserRole.مدير,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            );

            // إنشاء الخزائن الافتراضية
            modelBuilder.Entity<Treasury>().HasData(
                new Treasury
                {
                    TreasuryId = 1,
                    Name = "الخزينة اليومية",
                    Type = TreasuryType.خزينة_يومية,
                    SyrianPoundBalance = 0,
                    DollarBalance = 0,
                    IsActive = true
                },
                new Treasury
                {
                    TreasuryId = 2,
                    Name = "الخزينة الرئيسية",
                    Type = TreasuryType.خزينة_رئيسية,
                    SyrianPoundBalance = 0,
                    DollarBalance = 0,
                    IsActive = true
                }
            );

            // الإعدادات الافتراضية
            modelBuilder.Entity<Settings>().HasData(
                new Settings { SettingId = 1, Key = SettingsKeys.SubscriptionFee, Value = "50000", Description = "رسم الاشتراك الافتراضي" },
                new Settings { SettingId = 2, Key = SettingsKeys.TaxRate, Value = "0", Description = "معدل الضريبة" },
                new Settings { SettingId = 3, Key = SettingsKeys.Currency, Value = "ليرة سورية", Description = "العملة الافتراضية" },
                new Settings { SettingId = 4, Key = SettingsKeys.CompanyName, Value = "شركة أسامة للإنترنت", Description = "اسم الشركة" },
                new Settings { SettingId = 5, Key = SettingsKeys.CompanyAddress, Value = "دمشق - سوريا", Description = "عنوان الشركة" },
                new Settings { SettingId = 6, Key = SettingsKeys.CompanyPhone, Value = "011-1234567", Description = "هاتف الشركة" },
                new Settings { SettingId = 7, Key = SettingsKeys.DollarExchangeRate, Value = "15000", Description = "سعر صرف الدولار" },
                new Settings { SettingId = 8, Key = SettingsKeys.MinimumStockAlert, Value = "5", Description = "حد التنبيه للمخزون المنخفض" }
            );

            // باقات افتراضية
            modelBuilder.Entity<Package>().HasData(
                new Package { PackageId = 1, Name = "باقة أساسية", Description = "باقة إنترنت أساسية", Price = 25000, SpeedMbps = 10, DurationDays = 30 },
                new Package { PackageId = 2, Name = "باقة متوسطة", Description = "باقة إنترنت متوسطة", Price = 40000, SpeedMbps = 20, DurationDays = 30 },
                new Package { PackageId = 3, Name = "باقة متقدمة", Description = "باقة إنترنت متقدمة", Price = 60000, SpeedMbps = 50, DurationDays = 30 }
            );
        }
    }
}