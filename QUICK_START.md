# دليل التشغيل السريع - نظام أسامة

## 🚀 التشغيل السريع

### 1. التشغيل لأول مرة
```bash
# تشغيل ملف الإعداد الأولي
setup.bat
```

### 2. التشغيل العادي
```bash
# تشغيل التطبيق
run.bat
```

## 🔑 بيانات تسجيل الدخول الافتراضية

| المستخدم | اسم المستخدم | كلمة المرور | الدور |
|----------|-------------|------------|-------|
| مدير النظام | `admin` | `admin123` | مدير |
| المحاسب | `accountant` | `acc123` | محاسب |
| موظف مبيعات | `sales1` | `sales123` | موظف مبيعات |
| عامل تركيب | `installer1` | `install123` | عامل تركيب |
| موظف مخزون | `warehouse` | `warehouse123` | موظف مخزون |

## 📋 خطوات الاستخدام الأساسية

### 1. تسجيل الدخول
- افتح التطبيق
- أدخل اسم المستخدم وكلمة المرور
- أدخل الرصيد الافتتاحي للشفت (مثال: 100000)
- اضغط "تسجيل الدخول"

### 2. إنشاء اشتراك جديد
- انتقل إلى "المبيعات" → "اشتراك جديد"
- أدخل بيانات المشترك
- اختر نوع الراوتر (اختياري)
- اضغط "حفظ" أو "طباعة"

### 3. تسليم راوتر
- انتقل إلى "المبيعات" → "تسليم راوتر"
- اختر العميل من القائمة
- اختر نوع الراوتر والكابل
- اختر عامل التركيب والباقة
- اضغط "تسليم وحفظ"

### 4. تجديد باقة
- انتقل إلى "المبيعات" → "تجديد باقة"
- اختر العميل من القائمة أو من قائمة المستحقين للتجديد
- اختر نوع الباقة الجديدة
- اضغط "تجديد الباقة"

### 5. إغلاق الشفت
- اضغط على زر "إغلاق الشفت" في الشريط العلوي
- أدخل الرصيد الختامي الفعلي
- أضف ملاحظات إذا لزم الأمر
- اضغط "إغلاق الشفت"

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- SQL Server LocalDB (يتم تثبيته مع Visual Studio أو SQL Server Express)
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة قرص

### تثبيت المتطلبات
1. **تثبيت .NET 6.0:**
   - قم بتحميل .NET 6.0 SDK من: https://dotnet.microsoft.com/download/dotnet/6.0
   - قم بتثبيته واتبع التعليمات

2. **تثبيت SQL Server LocalDB:**
   - قم بتحميل SQL Server Express من: https://www.microsoft.com/sql-server/sql-server-downloads
   - اختر "Express" واتبع التعليمات
   - أو قم بتثبيت Visual Studio Community الذي يتضمن LocalDB

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ ".NET 6.0 غير مثبت"
**الحل:** قم بتثبيت .NET 6.0 SDK من الرابط المذكور أعلاه

#### 2. خطأ في قاعدة البيانات
**الحل:** 
```bash
# احذف قاعدة البيانات وأعد إنشاؤها
cd Osama.Data
dotnet ef database drop --startup-project ..\Osama.UI
dotnet ef database update --startup-project ..\Osama.UI
```

#### 3. مشاكل في الخطوط العربية
**الحل:** تأكد من وجود خطوط Cairo و Amiri في النظام

#### 4. بطء في الأداء
**الحل:** 
- تأكد من مواصفات النظام
- أغلق البرامج غير الضرورية
- تأكد من وجود مساحة كافية على القرص

## 📊 البيانات الافتراضية

### الباقات المتوفرة
- باقة أساسية - 1 ميجا: 25,000 ل.س
- باقة متوسطة - 2 ميجا: 40,000 ل.س  
- باقة متقدمة - 5 ميجا: 75,000 ل.س
- باقة مميزة - 10 ميجا: 120,000 ل.س

### المنتجات المتوفرة
- راوتر TP-Link AC1200: 60,000 ل.س
- راوتر Huawei HG8245H: 75,000 ل.س
- كابل شبكة Cat6: 20,000 ل.س
- كابل فايبر أوبتيك: 35,000 ل.س

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من ملفات السجل في مجلد Logs
3. تأكد من تحديث النظام لآخر إصدار

### معلومات إضافية:
- جميع المبالغ بالليرة السورية
- النظام يدعم RTL (من اليمين إلى اليسار)
- يمكن تخصيص الأسعار والباقات من إعدادات النظام

---

**© 2024 نظام أسامة - إدارة شركات الإنترنت والمحاسبة**