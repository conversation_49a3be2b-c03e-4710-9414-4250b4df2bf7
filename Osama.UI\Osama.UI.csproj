<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Osama.Models\Osama.Models.csproj" />
    <ProjectReference Include="..\Osama.Data\Osama.Data.csproj" />
    <ProjectReference Include="..\Osama.Services\Osama.Services.csproj" />
    <ProjectReference Include="..\Osama.ViewModels\Osama.ViewModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**" />
  </ItemGroup>

</Project>