using Microsoft.Toolkit.Mvvm.Input;
using Osama.Models;
using Osama.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace Osama.ViewModels
{
    /// <summary>
    /// نموذج عرض لوحة التحكم الرئيسية
    /// </summary>
    public class DashboardViewModel : BaseViewModel
    {
        private readonly AuthenticationService _authService;
        private readonly SalesService _salesService;
        private readonly FinancialService _financialService;
        private readonly InventoryService _inventoryService;

        // الإحصائيات
        private int _totalCustomers;
        private int _activeCustomers;
        private decimal _todaySales;
        private decimal _monthSales;
        private decimal _todayExpenses;
        private decimal _monthExpenses;
        private decimal _dailyTreasuryBalance;
        private decimal _mainTreasuryBalance;
        private int _lowStockProductsCount;

        // المجموعات
        private ObservableCollection<Customer> _customersDueForRenewal = new();
        private ObservableCollection<Product> _lowStockProducts = new();
        private ObservableCollection<Sale> _recentSales = new();

        public DashboardViewModel(
            AuthenticationService authService,
            SalesService salesService,
            FinancialService financialService,
            InventoryService inventoryService)
        {
            _authService = authService;
            _salesService = salesService;
            _financialService = financialService;
            _inventoryService = inventoryService;

            RefreshCommand = new AsyncRelayCommand(RefreshAsync);
            
            // تحديث البيانات عند التحميل
            _ = RefreshAsync();
        }

        #region الخصائص - الإحصائيات

        /// <summary>
        /// إجمالي العملاء
        /// </summary>
        public int TotalCustomers
        {
            get => _totalCustomers;
            set => SetProperty(ref _totalCustomers, value);
        }

        /// <summary>
        /// العملاء النشطون
        /// </summary>
        public int ActiveCustomers
        {
            get => _activeCustomers;
            set => SetProperty(ref _activeCustomers, value);
        }

        /// <summary>
        /// مبيعات اليوم
        /// </summary>
        public decimal TodaySales
        {
            get => _todaySales;
            set => SetProperty(ref _todaySales, value);
        }

        /// <summary>
        /// مبيعات الشهر
        /// </summary>
        public decimal MonthSales
        {
            get => _monthSales;
            set => SetProperty(ref _monthSales, value);
        }

        /// <summary>
        /// مصاريف اليوم
        /// </summary>
        public decimal TodayExpenses
        {
            get => _todayExpenses;
            set => SetProperty(ref _todayExpenses, value);
        }

        /// <summary>
        /// مصاريف الشهر
        /// </summary>
        public decimal MonthExpenses
        {
            get => _monthExpenses;
            set => SetProperty(ref _monthExpenses, value);
        }

        /// <summary>
        /// رصيد الخزينة اليومية
        /// </summary>
        public decimal DailyTreasuryBalance
        {
            get => _dailyTreasuryBalance;
            set => SetProperty(ref _dailyTreasuryBalance, value);
        }

        /// <summary>
        /// رصيد الخزينة الرئيسية
        /// </summary>
        public decimal MainTreasuryBalance
        {
            get => _mainTreasuryBalance;
            set => SetProperty(ref _mainTreasuryBalance, value);
        }

        /// <summary>
        /// عدد المنتجات منخفضة المخزون
        /// </summary>
        public int LowStockProductsCount
        {
            get => _lowStockProductsCount;
            set => SetProperty(ref _lowStockProductsCount, value);
        }

        #endregion

        #region الخصائص - المجموعات

        /// <summary>
        /// العملاء المستحقون للتجديد
        /// </summary>
        public ObservableCollection<Customer> CustomersDueForRenewal
        {
            get => _customersDueForRenewal;
            set => SetProperty(ref _customersDueForRenewal, value);
        }

        /// <summary>
        /// المنتجات منخفضة المخزون
        /// </summary>
        public ObservableCollection<Product> LowStockProducts
        {
            get => _lowStockProducts;
            set => SetProperty(ref _lowStockProducts, value);
        }

        /// <summary>
        /// المبيعات الأخيرة
        /// </summary>
        public ObservableCollection<Sale> RecentSales
        {
            get => _recentSales;
            set => SetProperty(ref _recentSales, value);
        }

        #endregion

        #region الأوامر

        /// <summary>
        /// أمر تحديث البيانات
        /// </summary>
        public IAsyncRelayCommand RefreshCommand { get; }

        #endregion

        #region المعلومات الحالية

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public User? CurrentUser => _authService.CurrentUser;

        /// <summary>
        /// الشفت الحالي
        /// </summary>
        public Shift? CurrentShift => _authService.CurrentShift;

        /// <summary>
        /// اسم المستخدم الحالي
        /// </summary>
        public string CurrentUserName => CurrentUser?.FullName ?? "غير محدد";

        /// <summary>
        /// دور المستخدم الحالي
        /// </summary>
        public string CurrentUserRole => CurrentUser?.Role.ToString() ?? "غير محدد";

        /// <summary>
        /// تاريخ بداية الشفت
        /// </summary>
        public string ShiftStartTime => CurrentShift?.StartTime.ToString("yyyy-MM-dd HH:mm") ?? "غير محدد";

        /// <summary>
        /// رصيد الشفت الحالي
        /// </summary>
        public decimal CurrentShiftBalance => CurrentShift?.ClosingBalance ?? 0;

        #endregion

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        public override async Task RefreshAsync()
        {
            await ExecuteAsync(async () =>
            {
                // تحديث الإحصائيات
                await LoadStatisticsAsync();

                // تحديث المجموعات
                await LoadCollectionsAsync();

                // إشعار بتحديث المعلومات الحالية
                OnPropertyChanged(nameof(CurrentUser));
                OnPropertyChanged(nameof(CurrentShift));
                OnPropertyChanged(nameof(CurrentUserName));
                OnPropertyChanged(nameof(CurrentUserRole));
                OnPropertyChanged(nameof(ShiftStartTime));
                OnPropertyChanged(nameof(CurrentShiftBalance));

            }, "جاري تحديث البيانات...");
        }

        /// <summary>
        /// تحميل الإحصائيات
        /// </summary>
        private async Task LoadStatisticsAsync()
        {
            var today = DateTime.Today;
            var monthStart = new DateTime(today.Year, today.Month, 1);
            var monthEnd = monthStart.AddMonths(1).AddDays(-1);

            // إحصائيات العملاء
            // هنا يجب إضافة الاستعلامات من قاعدة البيانات
            // TotalCustomers = await _customerService.GetTotalCustomersAsync();
            // ActiveCustomers = await _customerService.GetActiveCustomersAsync();

            // إحصائيات المبيعات
            TodaySales = await _salesService.GetTotalSalesAsync(today, today.AddDays(1).AddSeconds(-1));
            MonthSales = await _salesService.GetTotalSalesAsync(monthStart, monthEnd);

            // إحصائيات المصاريف
            // TodayExpenses = await _financialService.GetTotalExpensesAsync(today, today.AddDays(1).AddSeconds(-1));
            // MonthExpenses = await _financialService.GetTotalExpensesAsync(monthStart, monthEnd);

            // أرصدة الخزائن
            // var treasuries = await _financialService.GetTreasuriesAsync();
            // DailyTreasuryBalance = treasuries.FirstOrDefault(t => t.Type == TreasuryType.خزينة_يومية)?.SyrianPoundBalance ?? 0;
            // MainTreasuryBalance = treasuries.FirstOrDefault(t => t.Type == TreasuryType.خزينة_رئيسية)?.SyrianPoundBalance ?? 0;

            // المنتجات منخفضة المخزون
            var lowStockProducts = await _inventoryService.GetLowStockProductsAsync();
            LowStockProductsCount = lowStockProducts.Count();
        }

        /// <summary>
        /// تحميل المجموعات
        /// </summary>
        private async Task LoadCollectionsAsync()
        {
            // العملاء المستحقون للتجديد
            var dueCustomers = await _salesService.GetCustomersDueForRenewalAsync(7);
            CustomersDueForRenewal.Clear();
            foreach (var customer in dueCustomers.Take(10))
            {
                CustomersDueForRenewal.Add(customer);
            }

            // المنتجات منخفضة المخزون
            var lowStock = await _inventoryService.GetLowStockProductsAsync();
            LowStockProducts.Clear();
            foreach (var product in lowStock.Take(10))
            {
                LowStockProducts.Add(product);
            }

            // المبيعات الأخيرة
            var recentSales = await _salesService.GetSalesByPeriodAsync(DateTime.Today.AddDays(-7), DateTime.Now);
            RecentSales.Clear();
            foreach (var sale in recentSales.Take(10).OrderByDescending(s => s.SaleDate))
            {
                RecentSales.Add(sale);
            }
        }

        /// <summary>
        /// الحصول على لون المؤشر حسب الحالة
        /// </summary>
        /// <param name="current">القيمة الحالية</param>
        /// <param name="target">القيمة المستهدفة</param>
        /// <returns>لون المؤشر</returns>
        public string GetIndicatorColor(decimal current, decimal target)
        {
            if (current >= target * 0.8m)
                return "#4CAF50"; // أخضر
            else if (current >= target * 0.5m)
                return "#FF9800"; // برتقالي
            else
                return "#F44336"; // أحمر
        }

        /// <summary>
        /// الحصول على نسبة الإنجاز
        /// </summary>
        /// <param name="current">القيمة الحالية</param>
        /// <param name="target">القيمة المستهدفة</param>
        /// <returns>نسبة الإنجاز</returns>
        public double GetProgressPercentage(decimal current, decimal target)
        {
            if (target == 0) return 0;
            return Math.Min(100, (double)(current / target * 100));
        }
    }
}