using Microsoft.Toolkit.Mvvm.Input;
using Osama.Models;
using Osama.Services;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace Osama.ViewModels
{
    /// <summary>
    /// نموذج عرض تسجيل الدخول
    /// </summary>
    public class LoginViewModel : BaseViewModel
    {
        private readonly AuthenticationService _authService;
        private string _username = string.Empty;
        private string _password = string.Empty;
        private decimal _openingBalance;
        private bool _isLoginSuccessful;

        public LoginViewModel(AuthenticationService authService)
        {
            _authService = authService;
            LoginCommand = new AsyncRelayCommand(LoginAsync, () => CanLogin);
        }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        public string Username
        {
            get => _username;
            set
            {
                SetProperty(ref _username, value);
                LoginCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// كلمة المرور
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string Password
        {
            get => _password;
            set
            {
                SetProperty(ref _password, value);
                LoginCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// الرصيد الافتتاحي للشفت
        /// </summary>
        public decimal OpeningBalance
        {
            get => _openingBalance;
            set => SetProperty(ref _openingBalance, value);
        }

        /// <summary>
        /// هل تم تسجيل الدخول بنجاح
        /// </summary>
        public bool IsLoginSuccessful
        {
            get => _isLoginSuccessful;
            private set => SetProperty(ref _isLoginSuccessful, value);
        }

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public User? CurrentUser => _authService.CurrentUser;

        /// <summary>
        /// الشفت الحالي
        /// </summary>
        public Shift? CurrentShift => _authService.CurrentShift;

        /// <summary>
        /// أمر تسجيل الدخول
        /// </summary>
        public IAsyncRelayCommand LoginCommand { get; }

        /// <summary>
        /// هل يمكن تسجيل الدخول
        /// </summary>
        private bool CanLogin => !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password) && !IsBusy;

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        private async Task LoginAsync()
        {
            if (!ValidateData())
                return;

            var result = await ExecuteAsync(async () =>
            {
                // تسجيل الدخول
                var loginResult = await _authService.LoginAsync(Username, Password);
                
                if (!loginResult.Success)
                {
                    SetError(loginResult.Message);
                    return;
                }

                // بدء الشفت
                var shift = await _authService.StartShiftAsync(OpeningBalance);
                if (shift == null)
                {
                    SetError("فشل في بدء الشفت");
                    return;
                }

                IsLoginSuccessful = true;
                OnPropertyChanged(nameof(CurrentUser));
                OnPropertyChanged(nameof(CurrentShift));

            }, "جاري تسجيل الدخول...");
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public override bool ValidateData()
        {
            ClearError();

            if (string.IsNullOrWhiteSpace(Username))
            {
                SetError("اسم المستخدم مطلوب");
                return false;
            }

            if (string.IsNullOrWhiteSpace(Password))
            {
                SetError("كلمة المرور مطلوبة");
                return false;
            }

            if (OpeningBalance < 0)
            {
                SetError("الرصيد الافتتاحي لا يمكن أن يكون سالباً");
                return false;
            }

            return true;
        }

        /// <summary>
        /// إعادة تعيين النموذج
        /// </summary>
        public void Reset()
        {
            Username = string.Empty;
            Password = string.Empty;
            OpeningBalance = 0;
            IsLoginSuccessful = false;
            ClearError();
        }
    }
}