using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Osama.Data;

namespace Osama.DbMigrations
{
    /// <summary>
    /// مصنع سياق قاعدة البيانات للمايجريشن
    /// </summary>
    public class MigrationsDbContextFactory : IDesignTimeDbContextFactory<OsamaDbContext>
    {
        public OsamaDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<OsamaDbContext>();
            
            // استخدام SQLite للتطوير
            var connectionString = "Data Source=OsamaDB.db";
            
            optionsBuilder.UseSqlite(connectionString, b => b.MigrationsAssembly("Osama.DbMigrations"));
            
            return new OsamaDbContext(optionsBuilder.Options);
        }
    }
}