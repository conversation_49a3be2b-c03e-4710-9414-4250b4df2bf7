using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Osama.Data
{
    /// <summary>
    /// مصنع سياق قاعدة البيانات للتصميم
    /// </summary>
    public class OsamaDbContextFactory : IDesignTimeDbContextFactory<OsamaDbContext>
    {
        public OsamaDbContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<OsamaDbContext>();
            
            // استخدام connection string افتراضي للتصميم
            var connectionString = "Server=(localdb)\\mssqllocaldb;Database=OsamaDB;Trusted_Connection=true;MultipleActiveResultSets=true";
            
            optionsBuilder.UseSqlServer(connectionString);
            
            return new OsamaDbContext(optionsBuilder.Options);
        }
    }
}