@echo off
echo ========================================
echo       إعداد نظام أسامة لأول مرة
echo ========================================
echo.

echo جاري إعداد قاعدة البيانات...

:: الانتقال إلى مجلد Data
cd Osama.Data

:: إنشاء Migration جديد
echo إنشاء Migration...
dotnet ef migrations add InitialCreate --startup-project ..\Osama.UI

if %errorlevel% neq 0 (
    echo خطأ في إنشاء Migration
    pause
    exit /b 1
)

:: تطبيق Migration على قاعدة البيانات
echo تطبيق Migration على قاعدة البيانات...
dotnet ef database update --startup-project ..\Osama.UI

if %errorlevel% neq 0 (
    echo خطأ في تطبيق Migration
    pause
    exit /b 1
)

echo تم إعداد قاعدة البيانات بنجاح ✓

:: العودة إلى المجلد الرئيسي
cd ..

echo.
echo جاري إنشاء البيانات الأساسية...

:: تشغيل سكريبت إنشاء البيانات الأساسية
cd Osama.UI
dotnet run --seed-data

echo.
echo ========================================
echo تم إعداد النظام بنجاح!
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo يمكنك الآن تشغيل النظام باستخدام run.bat
echo ========================================

pause