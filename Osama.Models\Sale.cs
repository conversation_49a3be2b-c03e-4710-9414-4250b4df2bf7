using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج المبيعة - الاشتراكات الجديدة
    /// </summary>
    public class Sale
    {
        [Key]
        public int SaleId { get; set; }

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public int UserId { get; set; }

        public int? RouterProductId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubscriptionFee { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal RouterPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public DateTime SaleDate { get; set; } = DateTime.Now;

        public int ShiftId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public bool IsPrinted { get; set; } = false;

        // العلاقات
        public virtual Customer Customer { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual Product? RouterProduct { get; set; }
        public virtual Shift Shift { get; set; } = null!;
    }
}