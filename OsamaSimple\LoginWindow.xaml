<Window x:Class="OsamaSimple.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام أسامة - تسجيل الدخول" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Grid Background="#F5F5F5">
        <Border Background="White"
                CornerRadius="10"
                Margin="50"
                Padding="40">
            <Border.Effect>
                <DropShadowEffect Color="Gray" BlurRadius="10" ShadowDepth="3" Opacity="0.3"/>
            </Border.Effect>

            <StackPanel Orientation="Vertical" HorizontalAlignment="Stretch">
                <TextBlock Text="نظام أسامة" 
                           FontSize="24" 
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"
                           Foreground="#2196F3"/>
                
                <TextBlock Text="إدارة شركات الإنترنت" 
                           FontSize="16"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,30"
                           Foreground="#666"/>
                
                <TextBlock Text="اسم المستخدم:"
                           Margin="0,0,0,8"
                           FontSize="14"/>
                <TextBox x:Name="UsernameTextBox"
                         Height="40"
                         Padding="12"
                         Margin="0,0,0,20"
                         BorderBrush="#DDD"
                         BorderThickness="2"
                         FontSize="14"
                         VerticalContentAlignment="Center"/>

                <TextBlock Text="كلمة المرور:"
                           Margin="0,0,0,8"
                           FontSize="14"/>
                <PasswordBox x:Name="PasswordBox"
                             Height="40"
                             Padding="12"
                             Margin="0,0,0,25"
                             BorderBrush="#DDD"
                             BorderThickness="2"
                             FontSize="14"
                             VerticalContentAlignment="Center"/>

                <Button x:Name="LoginButton"
                        Content="تسجيل الدخول"
                        Height="45"
                        Background="#2196F3"
                        Foreground="White"
                        BorderThickness="0"
                        FontSize="16"
                        FontWeight="Bold"
                        Cursor="Hand"
                        Click="LoginButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#2196F3"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1976D2"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
                
                <TextBlock x:Name="ErrorMessage"
                           Foreground="Red"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"
                           Visibility="Collapsed"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
