<Window x:Class="OsamaSimple.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام أسامة - تسجيل الدخول" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Grid Background="LightBlue">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Width="350">

            <!-- العنوان -->
            <TextBlock Text="نظام أسامة"
                       FontSize="28"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"
                       Foreground="DarkBlue"/>

            <TextBlock Text="إدارة شركات الإنترنت"
                       FontSize="16"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,30"
                       Foreground="DarkBlue"/>

            <!-- حقل اسم المستخدم -->
            <TextBlock Text="اسم المستخدم:"
                       FontSize="16"
                       FontWeight="Bold"
                       Margin="0,0,0,5"/>
            <TextBox x:Name="UsernameTextBox"
                     Height="35"
                     FontSize="16"
                     Padding="8"
                     Margin="0,0,0,20"
                     Background="White"
                     BorderBrush="DarkBlue"
                     BorderThickness="2"/>

            <!-- حقل كلمة المرور -->
            <TextBlock Text="كلمة المرور:"
                       FontSize="16"
                       FontWeight="Bold"
                       Margin="0,0,0,5"/>
            <PasswordBox x:Name="PasswordBox"
                         Height="35"
                         FontSize="16"
                         Padding="8"
                         Margin="0,0,0,30"
                         Background="White"
                         BorderBrush="DarkBlue"
                         BorderThickness="2"/>

            <!-- زر تسجيل الدخول -->
            <Button x:Name="LoginButton"
                    Content="تسجيل الدخول"
                    Height="50"
                    FontSize="18"
                    FontWeight="Bold"
                    Background="DarkBlue"
                    Foreground="White"
                    BorderThickness="0"
                    Margin="0,0,0,20"
                    Click="LoginButton_Click"/>

            <!-- رسالة الخطأ -->
            <TextBlock x:Name="ErrorMessage"
                       FontSize="14"
                       Foreground="Red"
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       Visibility="Collapsed"/>

        </StackPanel>
    </Grid>
</Window>
