using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج سند القبض
    /// </summary>
    public class ReceiptVoucher
    {
        [Key]
        public int VoucherId { get; set; }

        [Required]
        [StringLength(50)]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        public int DistributorId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime VoucherDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual Distributor Distributor { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// نموذج سند الدفع
    /// </summary>
    public class PaymentVoucher
    {
        [Key]
        public int VoucherId { get; set; }

        [Required]
        [StringLength(50)]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        public int SupplierId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime VoucherDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual Supplier Supplier { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }
}