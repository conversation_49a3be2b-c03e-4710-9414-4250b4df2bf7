using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج تسليم الراوتر - عملية تسليم الراوتر للعميل
    /// </summary>
    public class RouterDelivery
    {
        [Key]
        public int DeliveryId { get; set; }

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int RouterProductId { get; set; }

        [Required]
        public int CableProductId { get; set; }

        [Required]
        public int PackageId { get; set; }

        [Required]
        public int InstallerId { get; set; } // عامل التركيب

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubscriptionFee { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PackagePrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public DateTime DeliveryDate { get; set; } = DateTime.Now;

        public int ShiftId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public bool IsPrinted { get; set; } = false;

        // العلاقات
        public virtual Customer Customer { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual Product RouterProduct { get; set; } = null!;
        public virtual Product CableProduct { get; set; } = null!;
        public virtual Package Package { get; set; } = null!;
        public virtual User Installer { get; set; } = null!;
        public virtual Shift Shift { get; set; } = null!;
    }
}