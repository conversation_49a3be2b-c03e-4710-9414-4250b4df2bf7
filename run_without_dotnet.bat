@echo off
echo ========================================
echo       نظام أسامة - تشغيل مباشر
echo ========================================
echo.

echo تحذير: هذا الملف للاختبار فقط
echo للاستخدام الكامل، يرجى تثبيت .NET 6.0 أولاً
echo.

echo جاري فتح المشروع في Visual Studio...

:: البحث عن Visual Studio
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" (
    start "" "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" "Osama.sln"
    echo تم فتح المشروع في Visual Studio Community 2022
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe" (
    start "" "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe" "Osama.sln"
    echo تم فتح المشروع في Visual Studio Professional 2022
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe" (
    start "" "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe" "Osama.sln"
    echo تم فتح المشروع في Visual Studio Enterprise 2022
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" (
    start "" "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" "Osama.sln"
    echo تم فتح المشروع في Visual Studio Community 2019
) else (
    echo لم يتم العثور على Visual Studio
    echo يرجى فتح ملف Osama.sln يدوياً في Visual Studio
    start explorer .
)

echo.
echo تعليمات التشغيل في Visual Studio:
echo 1. انتظر حتى يتم تحميل المشروع
echo 2. اضغط F5 أو اختر Debug ^> Start Debugging
echo 3. إذا طُلب منك تثبيت .NET 6.0، اتبع التعليمات
echo.

pause