using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج الخزينة
    /// </summary>
    public class Treasury
    {
        [Key]
        public int TreasuryId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public TreasuryType Type { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SyrianPoundBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DollarBalance { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<TreasuryTransaction> TreasuryTransactions { get; set; } = new List<TreasuryTransaction>();
    }

    /// <summary>
    /// معاملات الخزينة
    /// </summary>
    public class TreasuryTransaction
    {
        [Key]
        public int TransactionId { get; set; }

        [Required]
        public int TreasuryId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public TreasuryTransactionType Type { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SyrianPoundAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DollarAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ExchangeRate { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual Treasury Treasury { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// أنواع الخزينة
    /// </summary>
    public enum TreasuryType
    {
        خزينة_يومية = 1,
        خزينة_رئيسية = 2
    }

    /// <summary>
    /// أنواع معاملات الخزينة
    /// </summary>
    public enum TreasuryTransactionType
    {
        إيداع_ليرة = 1,
        سحب_ليرة = 2,
        إيداع_دولار = 3,
        سحب_دولار = 4,
        تحويل_من_صندوق = 5,
        تحويل_للخزينة_الرئيسية = 6,
        شراء_دولار = 7,
        بيع_دولار = 8
    }
}