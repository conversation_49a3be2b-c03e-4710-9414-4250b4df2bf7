using Microsoft.Toolkit.Mvvm.ComponentModel;
using Microsoft.Toolkit.Mvvm.Input;
using System.ComponentModel;

namespace Osama.ViewModels
{
    /// <summary>
    /// النموذج الأساسي لجميع ViewModels
    /// </summary>
    public abstract class BaseViewModel : ObservableObject
    {
        private bool _isBusy;
        private string _busyMessage = string.Empty;
        private string _errorMessage = string.Empty;
        private bool _hasError;

        /// <summary>
        /// هل النموذج مشغول
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                SetProperty(ref _isBusy, value);
                OnPropertyChanged(nameof(IsNotBusy));
            }
        }

        /// <summary>
        /// هل النموذج غير مشغول
        /// </summary>
        public bool IsNotBusy => !IsBusy;

        /// <summary>
        /// رسالة الانشغال
        /// </summary>
        public string BusyMessage
        {
            get => _busyMessage;
            set => SetProperty(ref _busyMessage, value);
        }

        /// <summary>
        /// رسالة الخطأ
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                SetProperty(ref _errorMessage, value);
                HasError = !string.IsNullOrEmpty(value);
            }
        }

        /// <summary>
        /// هل يوجد خطأ
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// مسح رسالة الخطأ
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// تعيين حالة الانشغال
        /// </summary>
        /// <param name="isBusy">حالة الانشغال</param>
        /// <param name="message">رسالة الانشغال</param>
        protected void SetBusy(bool isBusy, string message = "")
        {
            IsBusy = isBusy;
            BusyMessage = message;
        }

        /// <summary>
        /// تعيين رسالة خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        protected void SetError(string message)
        {
            ErrorMessage = message;
        }

        /// <summary>
        /// تنفيذ عملية مع معالجة الأخطاء
        /// </summary>
        /// <param name="action">العملية</param>
        /// <param name="busyMessage">رسالة الانشغال</param>
        /// <returns>نتيجة العملية</returns>
        protected async Task<bool> ExecuteAsync(Func<Task> action, string busyMessage = "جاري المعالجة...")
        {
            try
            {
                ClearError();
                SetBusy(true, busyMessage);

                await action();
                return true;
            }
            catch (Exception ex)
            {
                SetError($"حدث خطأ: {ex.Message}");
                return false;
            }
            finally
            {
                SetBusy(false);
            }
        }

        /// <summary>
        /// تنفيذ عملية مع إرجاع نتيجة ومعالجة الأخطاء
        /// </summary>
        /// <typeparam name="T">نوع النتيجة</typeparam>
        /// <param name="func">الدالة</param>
        /// <param name="busyMessage">رسالة الانشغال</param>
        /// <returns>النتيجة</returns>
        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> func, string busyMessage = "جاري المعالجة...")
        {
            try
            {
                ClearError();
                SetBusy(true, busyMessage);

                return await func();
            }
            catch (Exception ex)
            {
                SetError($"حدث خطأ: {ex.Message}");
                return default;
            }
            finally
            {
                SetBusy(false);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>هل البيانات صحيحة</returns>
        public virtual bool ValidateData()
        {
            ClearError();
            return true;
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        public virtual async Task RefreshAsync()
        {
            // يتم تنفيذها في الفئات المشتقة
            await Task.CompletedTask;
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public virtual void Cleanup()
        {
            // يتم تنفيذها في الفئات المشتقة
        }
    }
}