{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=OsamaDB;Trusted_Connection=true;MultipleActiveResultSets=true"}, "AppSettings": {"ApplicationName": "نظام أسامة", "Version": "1.0.0", "CompanyName": "شركة أسامة للإنترنت", "SupportEmail": "<EMAIL>", "DefaultLanguage": "ar-SA", "Theme": "Light"}, "DatabaseSettings": {"CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "AutoMigrateDatabase": true}, "SecuritySettings": {"PasswordMinLength": 6, "RequireDigit": false, "RequireUppercase": false, "RequireSpecialChar": false, "SessionTimeoutMinutes": 480, "MaxLoginAttempts": 5}, "BusinessSettings": {"DefaultSubscriptionFee": 50000, "DefaultCurrency": "SYP", "TaxRate": 0.0, "MinimumStockAlert": 5, "DefaultPackageDuration": 30, "BackupRetentionDays": 30}, "PrintSettings": {"DefaultPrinter": "", "PaperSize": "A4", "PrintMargins": {"Top": 20, "Bottom": 20, "Left": 20, "Right": 20}}, "ReportSettings": {"DefaultExportPath": "C:\\Reports", "MaxRecordsPerReport": 10000, "EnableAutoExport": false, "ExportFormats": ["PDF", "Excel"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}, "File": {"Path": "Logs/osama-{Date}.log", "RollingInterval": "Day", "RetainedFileCountLimit": 30}}}