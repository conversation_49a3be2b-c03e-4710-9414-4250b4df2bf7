<Page x:Class="Osama.UI.Pages.Sales.NewSubscriptionPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="اشتراك جديد"
      FlowDirection="RightToLeft">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="اشتراك جديد"
                   Style="{StaticResource HeaderStyle}"
                   Margin="0,0,0,24"/>

        <!-- المحتوى -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- نموذج الإدخال -->
                <StackPanel Grid.Column="0" Margin="0,0,16,0">
                    <!-- رسالة الخطأ -->
                    <Border Background="#FFEBEE"
                            BorderBrush="{StaticResource ErrorBrush}"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="12"
                            Margin="0,0,0,16"
                            Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                     Foreground="{StaticResource ErrorBrush}"
                                                     Width="20" Height="20"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding ErrorMessage}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- رسالة النجاح -->
                    <Border Background="#E8F5E8"
                            BorderBrush="{StaticResource SuccessBrush}"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="12"
                            Margin="0,0,0,16"
                            Visibility="{Binding HasSuccess, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CheckCircle"
                                                     Foreground="{StaticResource SuccessBrush}"
                                                     Width="20" Height="20"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding SuccessMessage}"
                                       Style="{StaticResource SuccessTextStyle}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- حقول الإدخال -->
                    <StackPanel>
                        <!-- اسم المشترك -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="اسم المشترك *"
                                 Text="{Binding CustomerName, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,16"/>

                        <!-- رقم الهاتف -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="رقم الهاتف"
                                 Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,16"/>

                        <!-- العنوان -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="العنوان"
                                 Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,16"/>

                        <!-- رسم الاشتراك -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="رسم الاشتراك (ل.س)"
                                 Text="{Binding SubscriptionFee, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,16"/>

                        <!-- نوع الراوتر -->
                        <ComboBox Style="{StaticResource InputComboBoxStyle}"
                                  materialDesign:HintAssist.Hint="نوع الراوتر (اختياري)"
                                  ItemsSource="{Binding RouterProducts}"
                                  SelectedItem="{Binding SelectedRouter}"
                                  DisplayMemberPath="Name"
                                  Margin="0,0,0,16"/>

                        <!-- ملاحظات -->
                        <TextBox Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="ملاحظات (اختياري)"
                                 Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="80"
                                 VerticalContentAlignment="Top"
                                 Margin="0,0,0,24"/>

                        <!-- الأزرار -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource PrimaryButtonStyle}"
                                    Content="حفظ"
                                    Command="{Binding SaveCommand}"
                                    IsEnabled="{Binding IsNotBusy}"
                                    Margin="0,0,8,0"/>
                            <Button Style="{StaticResource SecondaryButtonStyle}"
                                    Content="طباعة"
                                    Command="{Binding PrintCommand}"
                                    IsEnabled="{Binding CanPrint}"
                                    Margin="0,0,8,0"/>
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Content="مسح"
                                    Command="{Binding ClearCommand}"
                                    IsEnabled="{Binding IsNotBusy}"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <!-- معاينة الفاتورة -->
                <Border Grid.Column="1"
                        Background="#F8F9FA"
                        CornerRadius="8"
                        Padding="16">
                    <StackPanel>
                        <TextBlock Text="معاينة الفاتورة"
                                   Style="{StaticResource SubHeaderStyle}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,16"/>

                        <Border Background="White"
                                CornerRadius="4"
                                Padding="16"
                                materialDesign:ShadowAssist.ShadowDepth="Depth1">
                            <StackPanel>
                                <!-- رأس الفاتورة -->
                                <TextBlock Text="شركة أسامة للإنترنت"
                                           FontFamily="{StaticResource ArabicFontBold}"
                                           FontSize="16"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,8"/>
                                
                                <TextBlock Text="فاتورة اشتراك جديد"
                                           Style="{StaticResource SubHeaderStyle}"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,16"/>

                                <!-- تفاصيل الفاتورة -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المشترك:" FontSize="12" Margin="0,4"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CustomerName}" FontSize="12" Margin="8,4,0,4"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم الهاتف:" FontSize="12" Margin="0,4"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding PhoneNumber}" FontSize="12" Margin="8,4,0,4"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="العنوان:" FontSize="12" Margin="0,4"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Address}" FontSize="12" Margin="8,4,0,4"/>

                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="رسم الاشتراك:" FontSize="12" Margin="0,4"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SubscriptionFee, StringFormat='{}{0:N0} ل.س'}" FontSize="12" Margin="8,4,0,4"/>

                                    <TextBlock Grid.Row="4" Grid.Column="0" Text="نوع الراوتر:" FontSize="12" Margin="0,4"/>
                                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedRouter.Name}" FontSize="12" Margin="8,4,0,4"/>

                                    <Separator Grid.Row="5" Grid.ColumnSpan="2" Margin="0,8"/>
                                </Grid>

                                <!-- الإجمالي -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,8,0,0">
                                    <TextBlock Text="الإجمالي: "
                                               FontFamily="{StaticResource ArabicFontBold}"
                                               FontSize="14"/>
                                    <TextBlock Text="{Binding TotalAmount, StringFormat='{}{0:N0} ل.س'}"
                                               FontFamily="{StaticResource ArabicFontBold}"
                                               FontSize="14"
                                               Foreground="{StaticResource PrimaryBrush}"/>
                                </StackPanel>

                                <!-- تاريخ ووقت الإصدار -->
                                <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='التاريخ: yyyy/MM/dd - الوقت: HH:mm'}"
                                           FontSize="10"
                                           HorizontalAlignment="Center"
                                           Margin="0,16,0,0"
                                           Opacity="0.7"
                                           xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>