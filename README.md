# نظام أسامة - إدارة شركات الإنترنت والمحاسبة

## نظرة عامة
نظام أسامة هو تطبيق مكتبي متكامل مصمم خصيصاً لإدارة شركات الإنترنت والمحاسبة. يوفر النظام حلولاً شاملة لإدارة المبيعات، المخزون، العمليات المالية، والتقارير مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🔐 نظام المستخدمين والصلاحيات
- تعدد المستخدمين مع نظام تسجيل دخول آمن
- نظام صلاحيات متقدم (مدير، محاسب، موظف مبيعات، عامل تركيب، موظف مخزون)
- نظام الشفتات مع ربط العمليات بالمستخدم والشفت
- تشفير كلمات المرور باستخدام BCrypt

### 💰 إدارة المبيعات
- **اشتراك جديد**: تسجيل عملاء جدد مع رسوم الاشتراك
- **تسليم راوتر**: إدارة تسليم الراوترات للعملاء مع خصم المخزون
- **تجديد باقة**: تجديد باقات العملاء الحالية
- ربط العمليات بالشفتات والمستخدمين

### 🏦 العمليات المالية
- إدارة المصاريف بتصنيفات متعددة
- إغلاق الصناديق وتحويل الأموال للخزينة اليومية
- شراء وبيع الدولار مع تتبع أسعار الصرف
- نقل الأموال بين الخزائن (يومية ← رئيسية)
- شحن رصيد الموزعين
- إدارة السندات (قبض ودفع)

### 📦 إدارة المخزون
- تتبع شامل لحركة المنتجات
- تسليم المنتجات للعمال مع تتبع مخزون كل عامل
- أوامر الصيانة مع خصم المنتجات
- تنبيهات المخزون المنخفض
- جرد دوري للمنتجات

### 📊 التقارير والإحصائيات
- تقرير استهلاك الكابلات حسب العامل والفترة
- تقرير الأرباح والخسائر مع تصفية زمنية
- تقارير المخزون والحركة
- تقارير الصناديق والخزائن
- لوحة تحكم تفاعلية مع إحصائيات فورية

### 🎨 واجهة المستخدم
- تصميم عصري باستخدام Material Design
- دعم كامل للغة العربية (RTL)
- خطوط عربية واضحة (Cairo, Amiri)
- واجهات سهلة الاستخدام ومتجاوبة

## المتطلبات التقنية

### متطلبات النظام
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- SQL Server LocalDB أو SQL Server Express
- ذاكرة: 4 GB RAM (الحد الأدنى)
- مساحة القرص: 500 MB

### التقنيات المستخدمة
- **Framework**: .NET 6.0
- **UI Framework**: WPF مع Material Design
- **Architecture**: MVVM Pattern
- **Database**: SQL Server مع Entity Framework Core
- **Authentication**: BCrypt لتشفير كلمات المرور
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection

## هيكل المشروع

```
Osama/
├── Osama.Models/          # نماذج البيانات والكيانات
├── Osama.Data/            # طبقة الوصول للبيانات
│   ├── Repositories/      # مستودعات البيانات
│   └── OsamaDbContext.cs  # سياق قاعدة البيانات
├── Osama.Services/        # طبقة الخدمات والمنطق التجاري
├── Osama.ViewModels/      # نماذج العرض (MVVM)
├── Osama.UI/              # واجهة المستخدم
│   ├── Views/             # النوافذ الرئيسية
│   ├── Pages/             # الصفحات
│   ├── Resources/         # الموارد والأنماط
│   └── App.xaml           # تطبيق WPF الرئيسي
└── README.md              # هذا الملف
```

## التثبيت والتشغيل

### 1. متطلبات التطوير
```bash
# تثبيت .NET 6.0 SDK
# تثبيت Visual Studio 2022 أو Visual Studio Code
# تثبيت SQL Server LocalDB
```

### 2. استنساخ المشروع
```bash
git clone [repository-url]
cd Osama
```

### 3. استعادة الحزم
```bash
dotnet restore
```

### 4. تحديث قاعدة البيانات
```bash
cd Osama.Data
dotnet ef database update
```

### 5. تشغيل التطبيق
```bash
cd Osama.UI
dotnet run
```

## الاستخدام

### تسجيل الدخول الأولي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الرصيد الافتتاحي**: 0 (أو أي قيمة مناسبة)

### إنشاء مستخدمين جدد
1. سجل دخول كمدير
2. انتقل إلى إعدادات المستخدمين
3. أضف مستخدمين جدد مع تحديد الأدوار المناسبة

### بدء العمل اليومي
1. سجل دخول بحسابك
2. أدخل الرصيد الافتتاحي للشفت
3. ابدأ العمليات اليومية
4. أغلق الشفت في نهاية اليوم

## قاعدة البيانات

### الجداول الرئيسية
- **Users**: المستخدمون والصلاحيات
- **Customers**: العملاء والمشتركون
- **Products**: المنتجات (راوترات، كابلات، إلخ)
- **Packages**: باقات الإنترنت
- **Sales**: المبيعات والاشتراكات
- **Expenses**: المصاريف
- **Inventory**: المخزون والحركة
- **Treasury**: الخزائن والأرصدة

### النسخ الاحتياطي
يمكن إنشاء نسخة احتياطية من قاعدة البيانات باستخدام:
```sql
BACKUP DATABASE OsamaDB TO DISK = 'C:\Backup\OsamaDB.bak'
```

## الدعم والمساعدة

### المشاكل الشائعة
1. **خطأ في الاتصال بقاعدة البيانات**: تأكد من تثبيت SQL Server LocalDB
2. **مشاكل في الخطوط العربية**: تأكد من وجود خطوط Cairo و Amiri
3. **بطء في الأداء**: تحقق من مواصفات النظام

### التطوير والتخصيص
- يمكن إضافة وحدات جديدة بسهولة
- النظام مصمم ليكون قابلاً للتوسع
- يدعم إضافة تقارير مخصصة

## الترخيص
هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

---

**© 2024 نظام أسامة - جميع الحقوق محفوظة**