using System.Windows;

namespace Osama.UI.Views
{
    public partial class SimpleLoginWindow : Window
    {
        public SimpleLoginWindow()
        {
            InitializeComponent();
            UsernameTextBox.Focus();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            string username = UsernameTextBox.Text;
            string password = PasswordBox.Password;

            // تسجيل دخول مبسط للاختبار
            if (username == "admin" && password == "admin")
            {
                MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // فتح النافذة الرئيسية (مبسطة)
                var mainWindow = new SimpleMainWindow();
                mainWindow.Show();
                this.Close();
            }
            else
            {
                ErrorMessage.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                ErrorMessage.Visibility = Visibility.Visible;
            }
        }
    }
}
