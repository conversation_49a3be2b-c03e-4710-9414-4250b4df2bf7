using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج الشفت - شفتات العمل
    /// </summary>
    public class Shift
    {
        [Key]
        public int ShiftId { get; set; }

        [Required]
        public int UserId { get; set; }

        public DateTime StartTime { get; set; } = DateTime.Now;

        public DateTime? EndTime { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ClosingBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalExpenses { get; set; }

        public bool IsClosed { get; set; } = false;

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<RouterDelivery> RouterDeliveries { get; set; } = new List<RouterDelivery>();
        public virtual ICollection<PackageRenewal> PackageRenewals { get; set; } = new List<PackageRenewal>();
        public virtual ICollection<Expense> Expenses { get; set; } = new List<Expense>();
        public virtual ICollection<CashboxTransaction> CashboxTransactions { get; set; } = new List<CashboxTransaction>();
    }
}