{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\c#\\Osama.UI\\Osama.UI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj", "projectName": "Osama.Data", "projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj", "projectName": "Osama.Models", "projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj", "projectName": "Osama.Services", "projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj"}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "ClosedXML": {"target": "Package", "version": "[0.102.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}, "iText7": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.UI\\Osama.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.UI\\Osama.UI.csproj", "projectName": "Osama.UI", "projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.UI\\Osama.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Data\\Osama.Data.csproj"}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj"}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj"}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.ViewModels\\Osama.ViewModels.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.ViewModels\\Osama.ViewModels.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.ViewModels\\Osama.ViewModels.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.ViewModels\\Osama.ViewModels.csproj", "projectName": "Osama.ViewModels", "projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.ViewModels\\Osama.ViewModels.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.ViewModels\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Models\\Osama.Models.csproj"}, "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\c#\\Osama.Services\\Osama.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Toolkit.Mvvm": {"target": "Package", "version": "[7.1.2, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}