<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- الألوان المخصصة -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>

    <!-- نمط النافذة الرئيسية -->
    <Style x:Key="MainWindowStyle" TargetType="{x:Type Window}">
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="ResizeMode" Value="CanResize"/>
        <Setter Property="WindowState" Value="Maximized"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
    </Style>

    <!-- نمط النافذة الفرعية -->
    <Style x:Key="DialogWindowStyle" TargetType="{x:Type Window}">
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="ResizeMode" Value="NoResize"/>
        <Setter Property="WindowStartupLocation" Value="CenterOwner"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
    </Style>

    <!-- نمط البطاقة -->
    <Style x:Key="CardStyle" TargetType="{x:Type materialDesign:Card}">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="Background" Value="White"/>
    </Style>

    <!-- نمط الأزرار الرئيسية -->
    <Style x:Key="PrimaryButtonStyle" TargetType="{x:Type Button}" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="{StaticResource ArabicFontBold}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Height" Value="36"/>
    </Style>

    <!-- نمط الأزرار الثانوية -->
    <Style x:Key="SecondaryButtonStyle" TargetType="{x:Type Button}" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Height" Value="36"/>
    </Style>

    <!-- نمط حقول الإدخال -->
    <Style x:Key="InputTextBoxStyle" TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <!-- نمط ComboBox -->
    <Style x:Key="InputComboBoxStyle" TargetType="{x:Type ComboBox}" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
    </Style>

    <!-- نمط التسميات -->
    <Style x:Key="LabelStyle" TargetType="{x:Type Label}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="Margin" Value="4,8,4,4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- نمط العناوين -->
    <Style x:Key="HeaderStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFontBold}"/>
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>

    <!-- نمط العناوين الفرعية -->
    <Style x:Key="SubHeaderStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFontBold}"/>
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}"/>
        <Setter Property="Foreground" Value="#555555"/>
        <Setter Property="Margin" Value="8,4"/>
    </Style>

    <!-- نمط النصوص العادية -->
    <Style x:Key="BodyTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="Foreground" Value="#666666"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- نمط رسائل الخطأ -->
    <Style x:Key="ErrorTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}"/>
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- نمط رسائل النجاح -->
    <Style x:Key="SuccessTextStyle" TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}"/>
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- نمط DataGrid -->
    <Style x:Key="DataGridStyle" TargetType="{x:Type DataGrid}" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="Margin" Value="8"/>
    </Style>

    <!-- نمط شريط التقدم -->
    <Style x:Key="ProgressBarStyle" TargetType="{x:Type ProgressBar}" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="4"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- نمط أيقونات الحالة -->
    <Style x:Key="StatusIconStyle" TargetType="{x:Type materialDesign:PackIcon}">
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>

    <!-- نمط البطاقات الإحصائية -->
    <Style x:Key="StatCardStyle" TargetType="{x:Type materialDesign:Card}">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="MinHeight" Value="120"/>
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryLight"/>
    </Style>

</ResourceDictionary>