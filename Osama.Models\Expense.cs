using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج المصروف
    /// </summary>
    public class Expense
    {
        [Key]
        public int ExpenseId { get; set; }

        [Required]
        [StringLength(100)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public ExpenseCategory Category { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        public int UserId { get; set; }

        public DateTime ExpenseDate { get; set; } = DateTime.Now;

        public int ShiftId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(200)]
        public string? ReceiptNumber { get; set; }

        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual Shift Shift { get; set; } = null!;
    }

    /// <summary>
    /// تصنيفات المصاريف
    /// </summary>
    public enum ExpenseCategory
    {
        إيجار = 1,
        كهرباء = 2,
        صيانة = 3,
        رواتب = 4,
        مواصلات = 5,
        اتصالات = 6,
        قرطاسية = 7,
        أخرى = 8
    }
}