using Osama.Services;
using Osama.ViewModels;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace Osama.UI.Dialogs
{
    /// <summary>
    /// نافذة إغلاق الشفت
    /// </summary>
    public partial class CloseShiftDialog : Window
    {
        private readonly CloseShiftViewModel _viewModel;

        public CloseShiftDialog()
        {
            InitializeComponent();
            
            // إنشاء ViewModel
            var authService = App.GetService<AuthenticationService>();
            _viewModel = new CloseShiftViewModel(authService);
            DataContext = _viewModel;

            // ربط أحداث ViewModel
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        /// <summary>
        /// معالج تغيير خصائص ViewModel
        /// </summary>
        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(CloseShiftViewModel.IsShiftClosed) && _viewModel.IsShiftClosed)
            {
                DialogResult = true;
                Close();
            }
        }

        /// <summary>
        /// معالج زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            // إلغاء ربط الأحداث
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
            base.OnClosed(e);
        }
    }

    /// <summary>
    /// نموذج عرض إغلاق الشفت
    /// </summary>
    public class CloseShiftViewModel : BaseViewModel
    {
        private readonly AuthenticationService _authService;
        private decimal _closingBalance;
        private string _notes = string.Empty;
        private bool _isShiftClosed;

        public CloseShiftViewModel(AuthenticationService authService)
        {
            _authService = authService;
            
            // تعيين القيم الافتراضية
            if (_authService.CurrentShift != null)
            {
                _closingBalance = _authService.CurrentShift.ClosingBalance;
            }

            CloseShiftCommand = new Microsoft.Toolkit.Mvvm.Input.AsyncRelayCommand(CloseShiftAsync, () => CanCloseShift);
        }

        /// <summary>
        /// الرصيد الختامي
        /// </summary>
        public decimal ClosingBalance
        {
            get => _closingBalance;
            set
            {
                SetProperty(ref _closingBalance, value);
                OnPropertyChanged(nameof(HasDifference));
                OnPropertyChanged(nameof(DifferenceMessage));
                CloseShiftCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// الملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// هل تم إغلاق الشفت
        /// </summary>
        public bool IsShiftClosed
        {
            get => _isShiftClosed;
            private set => SetProperty(ref _isShiftClosed, value);
        }

        /// <summary>
        /// معلومات الشفت الحالي
        /// </summary>
        public string ShiftStartTime => _authService.CurrentShift?.StartTime.ToString("yyyy/MM/dd HH:mm") ?? "";
        public decimal OpeningBalance => _authService.CurrentShift?.OpeningBalance ?? 0;
        public decimal TotalSales => _authService.CurrentShift?.TotalSales ?? 0;
        public decimal TotalExpenses => _authService.CurrentShift?.TotalExpenses ?? 0;
        public decimal ExpectedBalance => OpeningBalance + TotalSales - TotalExpenses;

        /// <summary>
        /// هل يوجد فرق في الرصيد
        /// </summary>
        public bool HasDifference => Math.Abs(ClosingBalance - ExpectedBalance) > 0.01m;

        /// <summary>
        /// رسالة الفرق
        /// </summary>
        public string DifferenceMessage
        {
            get
            {
                var difference = ClosingBalance - ExpectedBalance;
                if (Math.Abs(difference) <= 0.01m)
                    return "الرصيد متطابق";
                
                return difference > 0 
                    ? $"زيادة في الرصيد: {difference:N0} ل.س"
                    : $"نقص في الرصيد: {Math.Abs(difference):N0} ل.س";
            }
        }

        /// <summary>
        /// أمر إغلاق الشفت
        /// </summary>
        public Microsoft.Toolkit.Mvvm.Input.IAsyncRelayCommand CloseShiftCommand { get; }

        /// <summary>
        /// هل يمكن إغلاق الشفت
        /// </summary>
        private bool CanCloseShift => !IsBusy && ClosingBalance >= 0;

        /// <summary>
        /// إغلاق الشفت
        /// </summary>
        private async Task CloseShiftAsync()
        {
            var result = await ExecuteAsync(async () =>
            {
                var success = await _authService.CloseShiftAsync(ClosingBalance, Notes);
                
                if (!success)
                {
                    SetError("فشل في إغلاق الشفت");
                    return;
                }

                IsShiftClosed = true;

            }, "جاري إغلاق الشفت...");
        }
    }
}