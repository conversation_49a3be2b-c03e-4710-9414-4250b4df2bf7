<Window x:Class="Osama.UI.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام أسامة"
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Border Background="White"
            CornerRadius="10"
            materialDesign:ShadowAssist.ShadowDepth="Depth3">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- الرأس -->
            <Border Grid.Row="0"
                    Background="{StaticResource PrimaryBrush}"
                    CornerRadius="10,10,0,0"
                    Padding="20">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountCircle"
                                             Width="64" Height="64"
                                             Foreground="White"
                                             Margin="0,0,0,16"/>
                    <TextBlock Text="نظام أسامة"
                               Style="{StaticResource HeaderStyle}"
                               Foreground="White"
                               FontSize="24"/>
                    <TextBlock Text="إدارة شركات الإنترنت والمحاسبة"
                               Style="{StaticResource BodyTextStyle}"
                               Foreground="White"
                               HorizontalAlignment="Center"
                               Opacity="0.9"/>
                </StackPanel>
            </Border>

            <!-- المحتوى -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto"
                          Padding="32">
                <StackPanel>
                    <!-- رسالة الخطأ -->
                    <Border Background="#FFEBEE"
                            BorderBrush="{StaticResource ErrorBrush}"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="12"
                            Margin="0,0,0,16"
                            Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                     Foreground="{StaticResource ErrorBrush}"
                                                     Width="20" Height="20"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding ErrorMessage}"
                                       Style="{StaticResource ErrorTextStyle}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- حقول الإدخال -->
                    <StackPanel>
                        <!-- اسم المستخدم -->
                        <TextBox x:Name="UsernameTextBox"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="اسم المستخدم"
                                 Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,16"/>

                        <!-- كلمة المرور -->
                        <PasswordBox x:Name="PasswordBox"
                                     Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                     materialDesign:HintAssist.Hint="كلمة المرور"
                                     FontFamily="{StaticResource ArabicFont}"
                                     FontSize="{StaticResource NormalFontSize}"
                                     Height="40"
                                     Margin="0,0,0,16"
                                     PasswordChanged="PasswordBox_PasswordChanged"/>

                        <!-- الرصيد الافتتاحي -->
                        <TextBox Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="الرصيد الافتتاحي"
                                 Text="{Binding OpeningBalance, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,24"/>
                    </StackPanel>

                    <!-- زر تسجيل الدخول -->
                    <Button Style="{StaticResource PrimaryButtonStyle}"
                            Content="تسجيل الدخول"
                            Command="{Binding LoginCommand}"
                            IsEnabled="{Binding IsNotBusy}"
                            Height="48"
                            FontSize="16"
                            Margin="0,0,0,16"/>

                    <!-- مؤشر التحميل -->
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                     Width="24" Height="24"
                                     IsIndeterminate="True"
                                     Margin="0,0,8,0"/>
                        <TextBlock Text="{Binding BusyMessage}"
                                   Style="{StaticResource BodyTextStyle}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>

            <!-- التذييل -->
            <Border Grid.Row="2"
                    Background="#F5F5F5"
                    CornerRadius="0,0,10,10"
                    Padding="20">
                <StackPanel>
                    <TextBlock Text="© 2024 نظام أسامة - جميع الحقوق محفوظة"
                               Style="{StaticResource BodyTextStyle}"
                               HorizontalAlignment="Center"
                               FontSize="12"
                               Foreground="#999999"/>
                    
                    <!-- زر الإغلاق -->
                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                            Content="إغلاق"
                            HorizontalAlignment="Center"
                            Margin="0,8,0,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>