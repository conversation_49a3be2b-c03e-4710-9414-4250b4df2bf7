using Microsoft.Toolkit.Mvvm.Input;
using Osama.Data.Repositories;
using Osama.Models;
using Osama.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace Osama.ViewModels.Sales
{
    /// <summary>
    /// نموذج عرض تسليم راوتر
    /// </summary>
    public class RouterDeliveryViewModel : BaseViewModel
    {
        private readonly SalesService _salesService;
        private readonly IUnitOfWork _unitOfWork;
        
        // الحقول
        private Customer? _selectedCustomer;
        private decimal _subscriptionFee = 50000;
        private Product? _selectedRouter;
        private User? _selectedInstaller;
        private Product? _selectedCable;
        private Package? _selectedPackage;
        private decimal _packagePrice;
        private string _notes = string.Empty;
        private bool _hasSuccess;
        private string _successMessage = string.Empty;
        private RouterDelivery? _lastDelivery;

        // المجموعات
        private ObservableCollection<Customer> _customers = new();
        private ObservableCollection<Product> _routerProducts = new();
        private ObservableCollection<User> _installers = new();
        private ObservableCollection<Product> _cableProducts = new();
        private ObservableCollection<Package> _packages = new();

        public RouterDeliveryViewModel(SalesService salesService, IUnitOfWork unitOfWork)
        {
            _salesService = salesService;
            _unitOfWork = unitOfWork;

            // إنشاء الأوامر
            DeliverCommand = new AsyncRelayCommand(DeliverAsync, () => CanDeliver);
            PrintCommand = new AsyncRelayCommand(PrintAsync, () => CanPrint);
            ClearCommand = new RelayCommand(Clear);

            // تحميل البيانات
            _ = LoadDataAsync();
        }

        #region الخصائص

        /// <summary>
        /// العميل المحدد
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار العميل")]
        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                SetProperty(ref _selectedCustomer, value);
                DeliverCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// رسم الاشتراك
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "رسم الاشتراك يجب أن يكون أكبر من الصفر")]
        public decimal SubscriptionFee
        {
            get => _subscriptionFee;
            set
            {
                SetProperty(ref _subscriptionFee, value);
                OnPropertyChanged(nameof(TotalAmount));
            }
        }

        /// <summary>
        /// الراوتر المحدد
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار نوع الراوتر")]
        public Product? SelectedRouter
        {
            get => _selectedRouter;
            set
            {
                SetProperty(ref _selectedRouter, value);
                OnPropertyChanged(nameof(TotalAmount));
                DeliverCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// عامل التركيب المحدد
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار عامل التركيب")]
        public User? SelectedInstaller
        {
            get => _selectedInstaller;
            set
            {
                SetProperty(ref _selectedInstaller, value);
                _ = LoadWorkerCablesAsync();
                DeliverCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// الكابل المحدد
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار نوع الكابل")]
        public Product? SelectedCable
        {
            get => _selectedCable;
            set
            {
                SetProperty(ref _selectedCable, value);
                DeliverCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// الباقة المحددة
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار نوع الباقة")]
        public Package? SelectedPackage
        {
            get => _selectedPackage;
            set
            {
                SetProperty(ref _selectedPackage, value);
                if (value != null)
                {
                    PackagePrice = value.Price;
                }
                DeliverCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// سعر الباقة
        /// </summary>
        public decimal PackagePrice
        {
            get => _packagePrice;
            set
            {
                SetProperty(ref _packagePrice, value);
                OnPropertyChanged(nameof(TotalAmount));
            }
        }

        /// <summary>
        /// الملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// هل يوجد رسالة نجاح
        /// </summary>
        public bool HasSuccess
        {
            get => _hasSuccess;
            set => SetProperty(ref _hasSuccess, value);
        }

        /// <summary>
        /// رسالة النجاح
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// المبلغ الإجمالي
        /// </summary>
        public decimal TotalAmount => SubscriptionFee + PackagePrice;

        /// <summary>
        /// العملاء
        /// </summary>
        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        /// <summary>
        /// منتجات الراوتر
        /// </summary>
        public ObservableCollection<Product> RouterProducts
        {
            get => _routerProducts;
            set => SetProperty(ref _routerProducts, value);
        }

        /// <summary>
        /// عمال التركيب
        /// </summary>
        public ObservableCollection<User> Installers
        {
            get => _installers;
            set => SetProperty(ref _installers, value);
        }

        /// <summary>
        /// منتجات الكابل
        /// </summary>
        public ObservableCollection<Product> CableProducts
        {
            get => _cableProducts;
            set => SetProperty(ref _cableProducts, value);
        }

        /// <summary>
        /// الباقات
        /// </summary>
        public ObservableCollection<Package> Packages
        {
            get => _packages;
            set => SetProperty(ref _packages, value);
        }

        #endregion

        #region الأوامر

        /// <summary>
        /// أمر التسليم
        /// </summary>
        public IAsyncRelayCommand DeliverCommand { get; }

        /// <summary>
        /// أمر الطباعة
        /// </summary>
        public IAsyncRelayCommand PrintCommand { get; }

        /// <summary>
        /// أمر المسح
        /// </summary>
        public ICommand ClearCommand { get; }

        /// <summary>
        /// هل يمكن التسليم
        /// </summary>
        private bool CanDeliver => SelectedCustomer != null && SelectedRouter != null && 
                                  SelectedInstaller != null && SelectedCable != null && 
                                  SelectedPackage != null && !IsBusy;

        /// <summary>
        /// هل يمكن الطباعة
        /// </summary>
        public bool CanPrint => _lastDelivery != null && !IsBusy;

        #endregion

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // تحميل العملاء
                var customers = await _unitOfWork.Customers.FindAsync(c => c.IsActive);
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                // تحميل منتجات الراوتر
                var routers = await _unitOfWork.Products.FindAsync(p => p.Category == ProductCategory.راوتر && p.IsActive && p.CurrentStock > 0);
                RouterProducts.Clear();
                foreach (var router in routers)
                {
                    RouterProducts.Add(router);
                }

                // تحميل عمال التركيب
                var installers = await _unitOfWork.Users.FindAsync(u => u.Role == UserRole.عامل_تركيب && u.IsActive);
                Installers.Clear();
                foreach (var installer in installers)
                {
                    Installers.Add(installer);
                }

                // تحميل الباقات
                var packages = await _unitOfWork.Packages.FindAsync(p => p.IsActive);
                Packages.Clear();
                foreach (var package in packages)
                {
                    Packages.Add(package);
                }

            }, "جاري تحميل البيانات...");
        }

        /// <summary>
        /// تحميل كابلات العامل
        /// </summary>
        private async Task LoadWorkerCablesAsync()
        {
            if (SelectedInstaller == null) return;

            await ExecuteAsync(async () =>
            {
                // تحميل الكابلات المتوفرة لدى العامل
                var workerInventory = await _unitOfWork.WorkerInventories.FirstOrDefaultAsync(wi => wi.UserId == SelectedInstaller.UserId && wi.IsActive);
                
                CableProducts.Clear();
                
                if (workerInventory != null)
                {
                    var workerItems = await _unitOfWork.WorkerInventoryItems.FindWithIncludeAsync(
                        wii => wii.WorkerInventoryId == workerInventory.WorkerInventoryId && wii.Quantity > 0,
                        wii => wii.Product
                    );

                    foreach (var item in workerItems.Where(i => i.Product.Category == ProductCategory.كابل))
                    {
                        CableProducts.Add(item.Product);
                    }
                }

            }, "جاري تحميل كابلات العامل...");
        }

        /// <summary>
        /// تسليم الراوتر
        /// </summary>
        private async Task DeliverAsync()
        {
            if (!ValidateData())
                return;

            var result = await ExecuteAsync(async () =>
            {
                var delivery = await _salesService.DeliverRouterAsync(
                    SelectedCustomer!.CustomerId,
                    SelectedRouter!.ProductId,
                    SelectedCable!.ProductId,
                    SelectedPackage!.PackageId,
                    SelectedInstaller!.UserId,
                    SubscriptionFee,
                    Notes
                );

                if (delivery == null)
                {
                    SetError("فشل في تسليم الراوتر");
                    return;
                }

                _lastDelivery = delivery;
                SetSuccess("تم تسليم الراوتر بنجاح");
                OnPropertyChanged(nameof(CanPrint));

                // تحديث البيانات
                await LoadDataAsync();

            }, "جاري تسليم الراوتر...");
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private async Task PrintAsync()
        {
            // إذا لم يتم التسليم، سلم أولاً
            if (_lastDelivery == null)
            {
                await DeliverAsync();
                if (_lastDelivery == null) return;
            }

            await ExecuteAsync(async () =>
            {
                // هنا يتم تنفيذ عملية الطباعة
                await Task.Delay(1000); // محاكاة عملية الطباعة

                SetSuccess("تم طباعة الفاتورة بنجاح");

            }, "جاري طباعة الفاتورة...");
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void Clear()
        {
            SelectedCustomer = null;
            SubscriptionFee = 50000;
            SelectedRouter = null;
            SelectedInstaller = null;
            SelectedCable = null;
            SelectedPackage = null;
            PackagePrice = 0;
            Notes = string.Empty;
            _lastDelivery = null;
            
            ClearError();
            ClearSuccess();
            
            OnPropertyChanged(nameof(CanPrint));
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public override bool ValidateData()
        {
            ClearError();
            ClearSuccess();

            if (SelectedCustomer == null)
            {
                SetError("يجب اختيار العميل");
                return false;
            }

            if (SelectedRouter == null)
            {
                SetError("يجب اختيار نوع الراوتر");
                return false;
            }

            if (SelectedInstaller == null)
            {
                SetError("يجب اختيار عامل التركيب");
                return false;
            }

            if (SelectedCable == null)
            {
                SetError("يجب اختيار نوع الكابل");
                return false;
            }

            if (SelectedPackage == null)
            {
                SetError("يجب اختيار نوع الباقة");
                return false;
            }

            if (SubscriptionFee <= 0)
            {
                SetError("رسم الاشتراك يجب أن يكون أكبر من الصفر");
                return false;
            }

            return true;
        }

        /// <summary>
        /// تعيين رسالة نجاح
        /// </summary>
        private void SetSuccess(string message)
        {
            SuccessMessage = message;
            HasSuccess = true;
        }

        /// <summary>
        /// مسح رسالة النجاح
        /// </summary>
        private void ClearSuccess()
        {
            SuccessMessage = string.Empty;
            HasSuccess = false;
        }
    }
}