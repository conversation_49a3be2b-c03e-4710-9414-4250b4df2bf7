<Application x:Class="Osama.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/SimpleLoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- الخطوط العربية -->
                <ResourceDictionary Source="Resources/Fonts.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- إعدادات RTL العامة -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS, Arial" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS, Arial" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style TargetType="{x:Type Page}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial Unicode MS, Arial" />
                <Setter Property="FontSize" Value="14" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>