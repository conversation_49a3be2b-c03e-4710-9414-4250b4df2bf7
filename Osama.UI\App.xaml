<Application x:Class="Osama.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- الخطوط العربية -->
                <ResourceDictionary Source="Resources/Fonts.xaml" />
                
                <!-- الأنماط المخصصة -->
                <ResourceDictionary Source="Resources/Styles.xaml" />
                
                <!-- القوالب -->
                <ResourceDictionary Source="Resources/Templates.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- إعدادات RTL العامة -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <Style TargetType="{x:Type Page}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="14" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>