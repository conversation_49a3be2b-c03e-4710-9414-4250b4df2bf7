@echo off
echo ========================================
echo       نظام أسامة - إدارة شركات الإنترنت
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...

:: التحقق من وجود .NET 6.0
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 6.0 غير مثبت على النظام
    echo يرجى تثبيت .NET 6.0 SDK من الرابط التالي:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo تم العثور على .NET 6.0 ✓

:: التحقق من وجود SQL Server LocalDB
sqlcmd -S "(localdb)\mssqllocaldb" -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: SQL Server LocalDB غير متوفر
    echo سيتم استخدام قاعدة بيانات في الذاكرة للاختبار
    echo لتثبيت SQL Server LocalDB، قم بتثبيت SQL Server Express
)

echo.
echo جاري استعادة الحزم...
dotnet restore

if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo تم استعادة الحزم بنجاح ✓

echo.
echo جاري بناء المشروع...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo تم بناء المشروع بنجاح ✓

echo.
echo جاري تشغيل التطبيق...
echo.

cd Osama.UI
dotnet run --configuration Release

if %errorlevel% neq 0 (
    echo خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

pause