@echo off
echo ========================================
echo       تثبيت .NET 6.0 تلقائياً
echo ========================================
echo.

echo جاري تحميل .NET 6.0 SDK...
echo.

:: إنشاء مجلد مؤقت
if not exist "temp" mkdir temp
cd temp

:: تحميل .NET 6.0 SDK
echo تحميل ملف التثبيت...
powershell -Command "Invoke-WebRequest -Uri 'https://download.visualstudio.microsoft.com/download/pr/17b6759f-1af0-41bc-ab12-209ba0377779/e8d02195dbf1434b940e0f05ae086453/dotnet-sdk-6.0.425-win-x64.exe' -OutFile 'dotnet-sdk-6.0.425-win-x64.exe'"

if exist "dotnet-sdk-6.0.425-win-x64.exe" (
    echo تم تحميل الملف بنجاح
    echo.
    echo جاري تثبيت .NET 6.0...
    echo يرجى اتباع تعليمات التثبيت التي ستظهر
    echo.
    
    :: تشغيل ملف التثبيت
    start /wait dotnet-sdk-6.0.425-win-x64.exe
    
    echo.
    echo تم الانتهاء من التثبيت
    echo يرجى إعادة تشغيل Command Prompt والمحاولة مرة أخرى
    
) else (
    echo فشل في تحميل الملف
    echo يرجى تحميل .NET 6.0 SDK يدوياً من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
)

:: تنظيف الملفات المؤقتة
cd ..
rmdir /s /q temp

echo.
echo بعد التثبيت، قم بتشغيل setup.bat مرة أخرى
pause