using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج الموزع
    /// </summary>
    public class Distributor
    {
        [Key]
        public int DistributorId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContactPerson { get; set; }

        [StringLength(15)]
        public string? PhoneNumber { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; }

        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<BalanceRecharge> BalanceRecharges { get; set; } = new List<BalanceRecharge>();
        public virtual ICollection<ReceiptVoucher> ReceiptVouchers { get; set; } = new List<ReceiptVoucher>();
    }

    /// <summary>
    /// نموذج شحن الرصيد
    /// </summary>
    public class BalanceRecharge
    {
        [Key]
        public int RechargeId { get; set; }

        [Required]
        public int DistributorId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        public DateTime RechargeDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // العلاقات
        public virtual Distributor Distributor { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }
}