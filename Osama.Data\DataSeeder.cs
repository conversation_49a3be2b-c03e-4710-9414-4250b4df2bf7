using Osama.Models;
using BCrypt.Net;

namespace Osama.Data
{
    /// <summary>
    /// فئة إنشاء البيانات الأساسية
    /// </summary>
    public static class DataSeeder
    {
        /// <summary>
        /// إنشاء البيانات الأساسية
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public static async Task SeedAsync(OsamaDbContext context)
        {
            // التأكد من إنشاء قاعدة البيانات
            await context.Database.EnsureCreatedAsync();

            // إنشاء المستخدم الافتراضي (المدير)
            if (!context.Users.Any())
            {
                var adminUser = new User
                {
                    Username = "admin",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    FullName = "مدير النظام",
                    Role = UserRole.مدير,
                    Email = "<EMAIL>",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                context.Users.Add(adminUser);
                await context.SaveChangesAsync();
            }

            // إنشاء الخزائن الافتراضية
            if (!context.Treasuries.Any())
            {
                var dailyTreasury = new Treasury
                {
                    Name = "الخزينة اليومية",
                    Type = TreasuryType.خزينة_يومية,
                    SyrianPoundBalance = 0,
                    DollarBalance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var mainTreasury = new Treasury
                {
                    Name = "الخزينة الرئيسية",
                    Type = TreasuryType.خزينة_رئيسية,
                    SyrianPoundBalance = 0,
                    DollarBalance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                context.Treasuries.AddRange(dailyTreasury, mainTreasury);
                await context.SaveChangesAsync();
            }

            // إنشاء باقات افتراضية
            if (!context.Packages.Any())
            {
                var packages = new[]
                {
                    new Package
                    {
                        Name = "باقة أساسية - 1 ميجا",
                        SpeedMbps = 1,
                        Price = 25000,
                        DurationDays = 30,
                        Description = "باقة إنترنت أساسية بسرعة 1 ميجا",
                        IsActive = true
                    },
                    new Package
                    {
                        Name = "باقة متوسطة - 2 ميجا",
                        SpeedMbps = 2,
                        Price = 40000,
                        DurationDays = 30,
                        Description = "باقة إنترنت متوسطة بسرعة 2 ميجا",
                        IsActive = true
                    },
                    new Package
                    {
                        Name = "باقة متقدمة - 5 ميجا",
                        SpeedMbps = 5,
                        Price = 75000,
                        DurationDays = 30,
                        Description = "باقة إنترنت متقدمة بسرعة 5 ميجا",
                        IsActive = true
                    },
                    new Package
                    {
                        Name = "باقة مميزة - 10 ميجا",
                        SpeedMbps = 10,
                        Price = 120000,
                        DurationDays = 30,
                        Description = "باقة إنترنت مميزة بسرعة 10 ميجا",
                        IsActive = true
                    }
                };

                context.Packages.AddRange(packages);
                await context.SaveChangesAsync();
            }

            // إنشاء منتجات افتراضية
            if (!context.Products.Any())
            {
                var products = new[]
                {
                    // راوترات
                    new Product
                    {
                        Name = "راوتر TP-Link AC1200",
                        Category = ProductCategory.راوتر,
                        PurchasePrice = 45000,
                        SalePrice = 60000,
                        CurrentStock = 50,
                        MinimumStock = 10,
                        Description = "راوتر لاسلكي بسرعة AC1200",
                        IsActive = true
                    },
                    new Product
                    {
                        Name = "راوتر Huawei HG8245H",
                        Category = ProductCategory.راوتر,
                        PurchasePrice = 55000,
                        SalePrice = 75000,
                        CurrentStock = 30,
                        MinimumStock = 5,
                        Description = "راوتر فايبر أوبتيك",
                        IsActive = true
                    },
                    
                    // كابلات
                    new Product
                    {
                        Name = "كابل شبكة Cat6 - 100 متر",
                        Category = ProductCategory.كابل,
                        PurchasePrice = 15000,
                        SalePrice = 20000,
                        CurrentStock = 100,
                        MinimumStock = 20,
                        Description = "كابل شبكة عالي الجودة",
                        IsActive = true
                    },
                    new Product
                    {
                        Name = "كابل فايبر أوبتيك - 100 متر",
                        Category = ProductCategory.كابل,
                        PurchasePrice = 25000,
                        SalePrice = 35000,
                        CurrentStock = 50,
                        MinimumStock = 10,
                        Description = "كابل فايبر أوبتيك للسرعات العالية",
                        IsActive = true
                    },

                    // اكسسوارات
                    new Product
                    {
                        Name = "موصل RJ45",
                        Category = ProductCategory.اكسسوارات,
                        PurchasePrice = 500,
                        SalePrice = 1000,
                        CurrentStock = 500,
                        MinimumStock = 100,
                        Description = "موصل شبكة RJ45",
                        IsActive = true
                    },
                    new Product
                    {
                        Name = "سبليتر 8 منافذ",
                        Category = ProductCategory.اكسسوارات,
                        PurchasePrice = 8000,
                        SalePrice = 12000,
                        CurrentStock = 25,
                        MinimumStock = 5,
                        Description = "سبليتر شبكة 8 منافذ",
                        IsActive = true
                    }
                };

                context.Products.AddRange(products);
                await context.SaveChangesAsync();
            }

            // إنشاء موردين افتراضيين
            if (!context.Suppliers.Any())
            {
                var suppliers = new[]
                {
                    new Supplier
                    {
                        Name = "شركة التقنية المتقدمة",
                        ContactPerson = "أحمد محمد",
                        PhoneNumber = "0944123456",
                        Email = "<EMAIL>",
                        Address = "دمشق - المزة",
                        IsActive = true
                    },
                    new Supplier
                    {
                        Name = "مؤسسة الشبكات الحديثة",
                        ContactPerson = "محمد علي",
                        PhoneNumber = "0933654321",
                        Email = "<EMAIL>",
                        Address = "حلب - الفرقان",
                        IsActive = true
                    }
                };

                context.Suppliers.AddRange(suppliers);
                await context.SaveChangesAsync();
            }

            // إنشاء موزعين افتراضيين
            if (!context.Distributors.Any())
            {
                var distributors = new[]
                {
                    new Distributor
                    {
                        Name = "موزع دمشق الرئيسي",
                        ContactPerson = "خالد أحمد",
                        PhoneNumber = "0911111111",
                        Email = "<EMAIL>",
                        Address = "دمشق - الميدان",
                        CurrentBalance = 0,
                        IsActive = true
                    },
                    new Distributor
                    {
                        Name = "موزع حلب الشمالي",
                        ContactPerson = "عمر حسن",
                        PhoneNumber = "0922222222",
                        Email = "<EMAIL>",
                        Address = "حلب - الصاخور",
                        CurrentBalance = 0,
                        IsActive = true
                    }
                };

                context.Distributors.AddRange(distributors);
                await context.SaveChangesAsync();
            }

            // إنشاء مستخدمين إضافيين للاختبار
            if (context.Users.Count() == 1) // فقط المدير موجود
            {
                var users = new[]
                {
                    new User
                    {
                        Username = "accountant",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("acc123"),
                        FullName = "محاسب النظام",
                        Role = UserRole.محاسب,
                        Email = "<EMAIL>",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new User
                    {
                        Username = "sales1",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("sales123"),
                        FullName = "موظف مبيعات 1",
                        Role = UserRole.موظف_مبيعات,
                        PhoneNumber = "**********",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new User
                    {
                        Username = "installer1",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("install123"),
                        FullName = "عامل تركيب 1",
                        Role = UserRole.عامل_تركيب,
                        PhoneNumber = "0933222222",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    },
                    new User
                    {
                        Username = "warehouse",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("warehouse123"),
                        FullName = "موظف مخزون",
                        Role = UserRole.موظف_مخزون,
                        PhoneNumber = "0955333333",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    }
                };

                context.Users.AddRange(users);
                await context.SaveChangesAsync();
            }

            Console.WriteLine("تم إنشاء البيانات الأساسية بنجاح!");
        }
    }
}