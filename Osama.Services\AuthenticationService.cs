using Osama.Data.Repositories;
using Osama.Models;
using BCrypt.Net;

namespace Osama.Services
{
    /// <summary>
    /// خدمة المصادقة وإدارة المستخدمين
    /// </summary>
    public class AuthenticationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private User? _currentUser;
        private Shift? _currentShift;

        public AuthenticationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public User? CurrentUser => _currentUser;

        /// <summary>
        /// الشفت الحالي
        /// </summary>
        public Shift? CurrentShift => _currentShift;

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة تسجيل الدخول</returns>
        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                // البحث عن المستخدم
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == username && u.IsActive);
                
                if (user == null)
                {
                    return new LoginResult { Success = false, Message = "اسم المستخدم غير صحيح" };
                }

                // التحقق من كلمة المرور
                if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                {
                    return new LoginResult { Success = false, Message = "كلمة المرور غير صحيحة" };
                }

                // تحديث تاريخ آخر دخول
                user.LastLoginDate = DateTime.Now;
                await _unitOfWork.Users.UpdateAsync(user);

                _currentUser = user;

                return new LoginResult { Success = true, Message = "تم تسجيل الدخول بنجاح", User = user };
            }
            catch (Exception ex)
            {
                return new LoginResult { Success = false, Message = $"خطأ في تسجيل الدخول: {ex.Message}" };
            }
        }

        /// <summary>
        /// بدء شفت جديد
        /// </summary>
        /// <param name="openingBalance">الرصيد الافتتاحي</param>
        /// <returns>الشفت الجديد</returns>
        public async Task<Shift?> StartShiftAsync(decimal openingBalance)
        {
            if (_currentUser == null)
                return null;

            try
            {
                // التحقق من عدم وجود شفت مفتوح
                var openShift = await _unitOfWork.Shifts.FirstOrDefaultAsync(s => s.UserId == _currentUser.UserId && !s.IsClosed);
                if (openShift != null)
                {
                    _currentShift = openShift;
                    return openShift;
                }

                // إنشاء شفت جديد
                var newShift = new Shift
                {
                    UserId = _currentUser.UserId,
                    StartTime = DateTime.Now,
                    OpeningBalance = openingBalance,
                    ClosingBalance = openingBalance,
                    TotalSales = 0,
                    TotalExpenses = 0,
                    IsClosed = false
                };

                await _unitOfWork.Shifts.AddAsync(newShift);
                _currentShift = newShift;

                return newShift;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// إغلاق الشفت الحالي
        /// </summary>
        /// <param name="closingBalance">الرصيد الختامي</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>نتيجة الإغلاق</returns>
        public async Task<bool> CloseShiftAsync(decimal closingBalance, string? notes = null)
        {
            if (_currentShift == null || _currentShift.IsClosed)
                return false;

            try
            {
                _currentShift.EndTime = DateTime.Now;
                _currentShift.ClosingBalance = closingBalance;
                _currentShift.Notes = notes;
                _currentShift.IsClosed = true;

                await _unitOfWork.Shifts.UpdateAsync(_currentShift);
                _currentShift = null;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        public void Logout()
        {
            _currentUser = null;
            _currentShift = null;
        }

        /// <summary>
        /// التحقق من الصلاحية
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>هل المستخدم لديه الصلاحية</returns>
        public bool HasPermission(UserRole requiredRole)
        {
            if (_currentUser == null)
                return false;

            // المدير لديه جميع الصلاحيات
            if (_currentUser.Role == UserRole.مدير)
                return true;

            return _currentUser.Role == requiredRole;
        }

        /// <summary>
        /// التحقق من الصلاحيات المتعددة
        /// </summary>
        /// <param name="allowedRoles">الأدوار المسموحة</param>
        /// <returns>هل المستخدم لديه إحدى الصلاحيات</returns>
        public bool HasAnyPermission(params UserRole[] allowedRoles)
        {
            if (_currentUser == null)
                return false;

            // المدير لديه جميع الصلاحيات
            if (_currentUser.Role == UserRole.مدير)
                return true;

            return allowedRoles.Contains(_currentUser.Role);
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="role">الدور</param>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>المستخدم الجديد</returns>
        public async Task<User?> CreateUserAsync(string username, string password, string fullName, UserRole role, string? phoneNumber = null, string? email = null)
        {
            try
            {
                // التحقق من عدم وجود المستخدم
                var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == username);
                if (existingUser != null)
                    return null;

                var newUser = new User
                {
                    Username = username,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(password),
                    FullName = fullName,
                    Role = role,
                    PhoneNumber = phoneNumber,
                    Email = email,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                return await _unitOfWork.Users.AddAsync(newUser);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>نتيجة التغيير</returns>
        public async Task<bool> ChangePasswordAsync(int userId, string oldPassword, string newPassword)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null)
                    return false;

                // التحقق من كلمة المرور القديمة
                if (!BCrypt.Net.BCrypt.Verify(oldPassword, user.PasswordHash))
                    return false;

                // تحديث كلمة المرور
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                await _unitOfWork.Users.UpdateAsync(user);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

    /// <summary>
    /// نتيجة تسجيل الدخول
    /// </summary>
    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
    }
}