using Osama.Services;
using Osama.ViewModels;
using System;
using System.Windows;
using System.Windows.Input;

namespace Osama.UI.Views
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly DashboardViewModel _viewModel;
        private readonly AuthenticationService _authService;

        public MainWindow()
        {
            InitializeComponent();
            
            // الحصول على الخدمات من DI Container
            _viewModel = App.GetService<DashboardViewModel>();
            _authService = App.GetService<AuthenticationService>();
            
            DataContext = _viewModel;

            // تحميل الصفحة الرئيسية
            LoadDashboard();
        }

        /// <summary>
        /// تحميل لوحة التحكم
        /// </summary>
        private void LoadDashboard()
        {
            var dashboardPage = new Pages.DashboardPage();
            MainFrame.Navigate(dashboardPage);
        }

        /// <summary>
        /// معالج أزرار التنقل
        /// </summary>
        private void NavigateButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is not FrameworkElement element || element.Tag is not string pageTag)
                return;

            try
            {
                switch (pageTag)
                {
                    case "Dashboard":
                        LoadDashboard();
                        break;
                    case "NewSubscription":
                        MainFrame.Navigate(new Pages.Sales.NewSubscriptionPage());
                        break;
                    case "RouterDelivery":
                        MainFrame.Navigate(new Pages.Sales.RouterDeliveryPage());
                        break;
                    case "PackageRenewal":
                        MainFrame.Navigate(new Pages.Sales.PackageRenewalPage());
                        break;
                    case "Expenses":
                        // MainFrame.Navigate(new Pages.Financial.ExpensesPage());
                        MessageBox.Show("صفحة المصروفات قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    case "CloseCashbox":
                        // MainFrame.Navigate(new Pages.Financial.CloseCashboxPage());
                        MessageBox.Show("صفحة إغلاق الصندوق قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    case "BuyDollar":
                        // MainFrame.Navigate(new Pages.Financial.BuyDollarPage());
                        MessageBox.Show("صفحة شراء الدولار قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    case "Inventory":
                        // MainFrame.Navigate(new Pages.Inventory.InventoryPage());
                        MessageBox.Show("صفحة المخزون قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    case "WorkerDelivery":
                        // MainFrame.Navigate(new Pages.Inventory.WorkerDeliveryPage());
                        MessageBox.Show("صفحة تسليم العامل قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    case "SalesReport":
                        // MainFrame.Navigate(new Pages.Reports.SalesReportPage());
                        MessageBox.Show("تقرير المبيعات قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    case "ProfitLossReport":
                        // MainFrame.Navigate(new Pages.Reports.ProfitLossReportPage());
                        MessageBox.Show("تقرير الأرباح والخسائر قيد التطوير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                    default:
                        MessageBox.Show($"الصفحة '{pageTag}' غير متوفرة حالياً", "تنبيه", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج زر إغلاق الشفت
        /// </summary>
        private async void CloseShiftButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق الشفت الحالي؟", "تأكيد", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // فتح نافذة إغلاق الشفت
                var closeShiftDialog = new Dialogs.CloseShiftDialog();
                var dialogResult = closeShiftDialog.ShowDialog();

                if (dialogResult == true)
                {
                    // تسجيل الخروج والعودة لنافذة تسجيل الدخول
                    _authService.Logout();
                    
                    var loginWindow = App.GetService<LoginWindow>();
                    loginWindow.Show();
                    
                    Close();
                }
            }
        }

        /// <summary>
        /// معالج زر التصغير
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// معالج زر الإغلاق
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// معالج سحب النافذة
        /// </summary>
        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        /// <summary>
        /// معالج اختصارات لوحة المفاتيح
        /// </summary>
        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            // F5 - تحديث
            if (e.Key == Key.F5)
            {
                _ = _viewModel.RefreshAsync();
            }
            // Ctrl+Q - إغلاق التطبيق
            else if (e.Key == Key.Q && Keyboard.Modifiers == ModifierKeys.Control)
            {
                CloseButton_Click(sender, e);
            }
            // Ctrl+Shift+L - تسجيل الخروج
            else if (e.Key == Key.L && Keyboard.Modifiers == (ModifierKeys.Control | ModifierKeys.Shift))
            {
                CloseShiftButton_Click(sender, e);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد
            _viewModel.Cleanup();
            base.OnClosed(e);
        }
    }
}