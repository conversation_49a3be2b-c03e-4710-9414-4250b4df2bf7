using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Osama.Models
{
    /// <summary>
    /// نموذج المنتج - يشمل الراوترات والكابلات وغيرها
    /// </summary>
    public class Product
    {
        [Key]
        public int ProductId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public ProductCategory Category { get; set; }

        [StringLength(50)]
        public string? Model { get; set; }

        [StringLength(50)]
        public string? Brand { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SalePrice { get; set; }

        public int CurrentStock { get; set; }

        public int MinimumStock { get; set; } = 5;

        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقات
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
        public virtual ICollection<PurchaseItem> PurchaseItems { get; set; } = new List<PurchaseItem>();
        public virtual ICollection<WorkerInventoryItem> WorkerInventoryItems { get; set; } = new List<WorkerInventoryItem>();
    }

    /// <summary>
    /// تصنيفات المنتجات
    /// </summary>
    public enum ProductCategory
    {
        راوتر = 1,
        كابل = 2,
        اكسسوارات = 3,
        قطع_غيار = 4,
        أخرى = 5
    }
}