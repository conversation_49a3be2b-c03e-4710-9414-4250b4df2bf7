{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"OsamaSimple/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Hosting": "7.0.0", "Osama.Data": "1.0.0", "Osama.Models": "1.0.0", "Osama.Services": "1.0.0", "Osama.ViewModels": "1.0.0"}, "runtime": {"OsamaSimple.dll": {}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "1.700.22.46903"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "ClosedXML/0.102.0": {"dependencies": {"DocumentFormat.OpenXml": "2.16.0", "ExcelNumberFormat": "1.1.0", "SixLabors.Fonts": "1.0.0-beta19", "System.IO.Packaging": "6.0.0", "System.Net.Http": "4.3.4", "System.Text.RegularExpressions": "4.3.1", "XLParser": "1.5.2"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.102.0.0", "fileVersion": "0.102.0.0"}}}, "DocumentFormat.OpenXml/2.16.0": {"dependencies": {"System.IO.Packaging": "6.0.0"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.16.0.0", "fileVersion": "2.16.0.0"}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Irony.NetCore/1.0.11": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Reflection": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Numerics": "4.3.0"}, "runtime": {"lib/netstandard1.6/Irony.dll": {"assemblyVersion": "1.0.11.0", "fileVersion": "1.0.11.0"}}}, "itext/8.0.2": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "1.1.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Process": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "itext.commons": "8.0.2"}, "runtime": {"lib/netstandard2.0/itext.barcodes.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.bouncy-castle-connector.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.forms.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.io.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.kernel.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.layout.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.pdfa.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.sign.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.styledxmlparser.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.svg.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "itext.commons/8.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "7.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/itext.commons.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "itext7/8.0.2": {"dependencies": {"itext": "8.0.2"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.1": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite.Core/7.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Microsoft.EntityFrameworkCore/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {}, "Microsoft.EntityFrameworkCore.Design/7.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "7.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "7.0.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.0"}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Hosting/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.Configuration.CommandLine": "7.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Logging.Console": "7.0.0", "Microsoft.Extensions.Logging.Debug": "7.0.0", "Microsoft.Extensions.Logging.EventLog": "7.0.0", "Microsoft.Extensions.Logging.EventSource": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Console/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Diagnostics.EventLog": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Toolkit.Mvvm/7.1.2": {"runtime": {"lib/net5.0/Microsoft.Toolkit.Mvvm.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.2.1"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SixLabors.Fonts/1.0.0-beta19": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.core/2.1.2": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"runtimeTargets": {"runtimes/alpine-arm/native/libe_sqlite3.so": {"rid": "alpine-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-arm64/native/libe_sqlite3.so": {"rid": "alpine-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.CodeDom/4.4.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.922.41905"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/7.0.0": {"runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Packaging/6.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "XLParser/1.5.2": {"dependencies": {"Irony.NetCore": "1.0.11", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.6/XLParser.dll": {"assemblyVersion": "1.5.2.0", "fileVersion": "1.5.2.0"}}}, "Osama.Data/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore.Design": "7.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "7.0.0", "Microsoft.EntityFrameworkCore.Tools": "7.0.0", "Osama.Models": "1.0.0"}, "runtime": {"Osama.Data.dll": {}}}, "Osama.Models/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.0", "System.ComponentModel.Annotations": "5.0.0"}, "runtime": {"Osama.Models.dll": {}}}, "Osama.Services/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "ClosedXML": "0.102.0", "Microsoft.Data.SqlClient": "5.1.1", "Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Osama.Data": "1.0.0", "Osama.Models": "1.0.0", "itext7": "8.0.2"}, "runtime": {"Osama.Services.dll": {}}}, "Osama.ViewModels/1.0.0": {"dependencies": {"Microsoft.Toolkit.Mvvm": "7.1.2", "Osama.Models": "1.0.0", "Osama.Services": "1.0.0", "System.ComponentModel.Annotations": "5.0.0"}, "runtime": {"Osama.ViewModels.dll": {}}}}}, "libraries": {"OsamaSimple/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "ClosedXML/0.102.0": {"type": "package", "serviceable": true, "sha512": "sha512-y4AiOq/XwJxbTV8wb9h32Ze9zme7oN6Qa7mpgzlCTK847d6P8BC3NDuqBj3X9RnC/CXmSwsmRS/vrIz7aHwijQ==", "path": "closedxml/0.102.0", "hashPath": "closedxml.0.102.0.nupkg.sha512"}, "DocumentFormat.OpenXml/2.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-RhpnDgyyx1KjZm98T3w3bSXFHG/7ZNUaVmz4NAUA+jmV3PcVNZeW87Y04CpBNLdDHEMSspirfo0B5kLRaoE97w==", "path": "documentformat.openxml/2.16.0", "hashPath": "documentformat.openxml.2.16.0.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Irony.NetCore/1.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-/2MCkqdhEjPiSTZKYXg6u9AO9o3m68biqcFHAnM28WdnrcZii2vYbmKPzT5ejByyV72FUlygOtHBMt8SLnUyNw==", "path": "irony.netcore/1.0.11", "hashPath": "irony.netcore.1.0.11.nupkg.sha512"}, "itext/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-79IRTeZZirzRDEtB1PtUMSpsxaJiFz5rAXY4/VtSipsi0qlQpJ/88jxd2SsfX0IggH+wlrsiAc0Y8X2et2T+Bg==", "path": "itext/8.0.2", "hashPath": "itext.8.0.2.nupkg.sha512"}, "itext.commons/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/F7j7XERA84XgIXDb4j/so1GagAToraVKZIRAdk/ibkvYoU1uwuILU2Ly28sSFf12NN7WIBQHKSk7njJl7Nk8g==", "path": "itext.commons/8.0.2", "hashPath": "itext.commons.8.0.2.nupkg.sha512"}, "itext7/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IuFfSlMSTLqVGCIsxpcDL9kCtEfppSBjq4V+RgfrxnqEC+iNF4tCHiIdl8quyzTDbaHF4vNufxe2NXMJMkPlSw==", "path": "itext7/8.0.2", "hashPath": "itext7.8.0.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "path": "microsoft.data.sqlclient/5.1.1", "hashPath": "microsoft.data.sqlclient.5.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WC7SANtaFTmQ/WPyhrE96h88MrH28C7kTBenDf5Eo+IR6CbWM0Uw2pR2++tyYr3qe/zSIsIYroEupB1mLg/adw==", "path": "microsoft.data.sqlite.core/7.0.0", "hashPath": "microsoft.data.sqlite.core.7.0.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bl6KYfbFSIW3QIRHAp931iR5h01qHjKghdpAtncwbzNUs0+IUZ+XfwkIU0sQsR33ufGvi3u4dZMIYYFysjpHAA==", "path": "microsoft.dotnet.platformabstractions/1.1.0", "hashPath": "microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9W+IfmAzMrp2ZpKZLhgTlWljSBM9Erldis1us61DAGi+L7Q6vilTbe1G2zDxtYO8F2H0I0Qnupdx5Cp4s2xoZw==", "path": "microsoft.entityframeworkcore/7.0.0", "hashPath": "microsoft.entityframeworkcore.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pfu3Zjj5+d2Gt27oE9dpGiF/VobBB+s5ogrfI9sBsXQE1SG49RqVz5+IyeNnzhyejFrPIQsPDRMchhcojy4Hbw==", "path": "microsoft.entityframeworkcore.abstractions/7.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qkd2H+jLe37o5ku+LjT6qf7kAHY75Yfn2bBDQgqr13DTOLYpEy1Mt93KPFjaZvIu/srEcbfGGMRL7urKm5zN8Q==", "path": "microsoft.entityframeworkcore.analyzers/7.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fEEU/zZ/VblZRQxHNZxgGKVtEtOGqEAmuHkACV1i0H031bM8PQKTS7PlKPVOgg0C1v+6yeHoIAGGgbAvG9f7kw==", "path": "microsoft.entityframeworkcore.design/7.0.0", "hashPath": "microsoft.entityframeworkcore.design.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "path": "microsoft.entityframeworkcore.relational/7.0.0", "hashPath": "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tufYoEHetVeALPRqzCi4YukL+uoS0pBi/6m3uSCuM5NxO/tTbuizvvBRa3qJNYZOvF3K7m4lUmmcCymKr3JSSQ==", "path": "microsoft.entityframeworkcore.sqlite/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aUClrz1PT06fPDY+9f2IeDhYXj3/oPxM0r3I6syiyP3Th59hObVI0QsRu5+y7FbJIkO3NgyAi+e2vzWGhmBVbQ==", "path": "microsoft.entityframeworkcore.sqlite.core/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.core.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+43PF5c5s8VQQl1ZKCQmkRGA/C1agHki31DgJGS0In+KI/xiUanqN3Z8v4BKTWBQYG3W9/me7Z199gOX6OKb+g==", "path": "microsoft.entityframeworkcore.sqlserver/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlserver.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DtLJ0usm8NdPbRDxvNUBAYgnvqhodr/HPb461I+jrgHw5ZKF0vRTaokNth2Zy9xiw1ZTpT4c+S40f7AHWakODg==", "path": "microsoft.entityframeworkcore.tools/7.0.0", "hashPath": "microsoft.entityframeworkcore.tools.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "path": "microsoft.extensions.configuration/7.0.0", "hashPath": "microsoft.extensions.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tgU4u7bZsoS9MKVRiotVMAwHtbREHr5/5zSEV+JPhg46+ox47Au84E3D2IacAaB0bk5ePNaNieTlPrfjbbRJkg==", "path": "microsoft.extensions.configuration.binder/7.0.0", "hashPath": "microsoft.extensions.configuration.binder.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-a8Iq8SCw5m8W5pZJcPCgBpBO4E89+NaObPng+ApIhrGSv9X4JPrcFAaGM4sDgR0X83uhLgsNJq8VnGP/wqhr8A==", "path": "microsoft.extensions.configuration.commandline/7.0.0", "hashPath": "microsoft.extensions.configuration.commandline.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RIkfqCkvrAogirjsqSrG1E1FxgrLsOZU2nhRbl07lrajnxzSU2isj2lwQah0CtCbLWo/pOIukQzM1GfneBUnxA==", "path": "microsoft.extensions.configuration.environmentvariables/7.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "path": "microsoft.extensions.configuration.json/7.0.0", "hashPath": "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-33HPW1PmB2RS0ietBQyvOxjp4O3wlt+4tIs8KPyMn1kqp04goiZGa7+3mc69NRLv6bphkLDy0YR7Uw3aZyf8Zw==", "path": "microsoft.extensions.configuration.usersecrets/7.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4nFc8xCfK26G524ioreZvz/IeIKN/gY1LApoGpaIThKqBdTwauUo4ETCf12lQcoefijqe3Imnfvnk31IezFatg==", "path": "microsoft.extensions.hosting/7.0.0", "hashPath": "microsoft.extensions.hosting.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-43n9Je09z0p/7ViPxfRqs5BUItRLNVh5b6JH40F2Agkh2NBsY/jpNYTtbCcxrHCsA3oRmbR6RJBzUutB4VZvNQ==", "path": "microsoft.extensions.hosting.abstractions/7.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FLDA0HcffKA8ycoDQLJuCNGIE42cLWPxgdQGRBaSzZrYTkMBjnf9zrr8pGT06psLq9Q+RKWmmZczQ9bCrXEBcA==", "path": "microsoft.extensions.logging.configuration/7.0.0", "hashPath": "microsoft.extensions.logging.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qt5n8bHLZPUfuRnFxJKW5q9ZwOTncdh96rtWzWpX3Y/064MlxzCSw2ELF5Jlwdo+Y4wK3I47NmUTFsV7Sg8rqg==", "path": "microsoft.extensions.logging.console/7.0.0", "hashPath": "microsoft.extensions.logging.console.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tFGGyPDpJ8ZdQdeckCArP7nZuoY3am9zJWuvp4OD1bHq65S0epW9BNHzAWeaIO4eYwWnGm1jRNt3vRciH8H6MA==", "path": "microsoft.extensions.logging.debug/7.0.0", "hashPath": "microsoft.extensions.logging.debug.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rp7cYL9xQRVTgjMl77H5YDxszAaO+mlA+KT0BnLSVhuCoKQQOOs1sSK2/x8BK2dZ/lKeAC/CVF+20Ef2dpKXwg==", "path": "microsoft.extensions.logging.eventlog/7.0.0", "hashPath": "microsoft.extensions.logging.eventlog.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MxQXndQFviIyOPqyMeLNshXnmqcfzEHE2wWcr7BF1unSisJgouZ3tItnq+aJLGPojrW8OZSC/ZdRoR6wAq+c7w==", "path": "microsoft.extensions.logging.eventsource/7.0.0", "hashPath": "microsoft.extensions.logging.eventsource.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-95UnxZkkFdXxF6vSrtJsMHCzkDeSMuUWGs2hDT54cX+U5eVajrCJ3qLyQRW+CtpTt5OJ8bmTvpQVHu1DLhH+cA==", "path": "microsoft.extensions.options.configurationextensions/7.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "path": "microsoft.identitymodel.abstractions/6.24.0", "hashPath": "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "path": "microsoft.identitymodel.logging/6.24.0", "hashPath": "microsoft.identitymodel.logging.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "path": "microsoft.identitymodel.tokens/6.24.0", "hashPath": "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Toolkit.Mvvm/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-tUQn6ar9oP7kmYaZ6pWabxpQaqw95Bsf+GG7prEL0y5MV5F60z76lI/Ppps9zcTqwd7e5CrdZBcdfor9WuvohA==", "path": "microsoft.toolkit.mvvm/7.1.2", "hashPath": "microsoft.toolkit.mvvm.7.1.2.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lw1/VwLH1yxz6SfFEjVRCN0pnflLEsWgnV4qsdJ512/HhTwnKXUG+zDQ4yTO3K/EJQemGoNaBHX5InISNKTzUQ==", "path": "microsoft.win32.registry/4.3.0", "hashPath": "microsoft.win32.registry.4.3.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SixLabors.Fonts/1.0.0-beta19": {"type": "package", "serviceable": true, "sha512": "sha512-ECJoayRT54VkJNoKBwZZllGvBIvxmnUFXsUCqHU8uAjAFrcdtxNpQ5Auyzw3d4+B+Q3KivKIrd7uO0qiNx/glg==", "path": "sixlabors.fonts/1.0.0-beta19", "hashPath": "sixlabors.fonts.1.0.0-beta19.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "path": "sqlitepclraw.core/2.1.2", "hashPath": "sqlitepclraw.core.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "path": "system.identitymodel.tokens.jwt/6.24.0", "hashPath": "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7OkTRIjqIjAKu6ef/fuj8ynCZTPcTYZnvHaq48bniACgXXJogmEoIc56YCDNTc14xhsbLmgpS3KP+evbsUa2g==", "path": "system.io.packaging/6.0.0", "hashPath": "system.io.packaging.6.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "XLParser/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-8tnUCmMbn6c/HGuHFyq1sulHym8YReTKDNXFFQk0U8nldNx6RGrwRvLh0+aQjEc7YoC3Q1ztH1npLt7dClKqjw==", "path": "xlparser/1.5.2", "hashPath": "xlparser.1.5.2.nupkg.sha512"}, "Osama.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Osama.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Osama.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Osama.ViewModels/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}